import 'package:city_academy/core/util/enums.dart';
import 'package:city_academy/screens/search_student/controller/bloc/search_students_state.dart';
import 'package:city_academy/screens/search_student/controller/search_students_service.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class SearchStudentsBloc extends Cubit<SearchStudentsState> {
  SearchStudentsBloc() : super(SearchStudentsStateEmpty());

  void fetchStudent() async {
    emit(state.copyWith(loading: true, error: null));
    final either = await SearchStudentsService().call(state.type, state.query);

    either.fold((l) => emit(state.copyWith(error: l, loading: false)),
        (r) => emit(state.copyWith(students: r, loading: false)));
  }

  void updateQuery(String value) {
    emit(state.copyWith(query: value));
    fetchStudent();
  }

  Future<void> updateType(SearchType value) async {
    emit(state.copyWith(type: value));
    fetchStudent();
  }
}
