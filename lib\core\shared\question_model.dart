import 'dart:convert';

class Question {
  int id;
  String question;
  bool? answer;
  String? comments;

  Question({
    required this.id,
    required this.question,
    this.answer,
    this.comments,
  });

  Question copyWith({
    int? id,
    String? question,
    bool? answer,
    String? comments,
  }) {
    return Question(
      id: id ?? this.id,
      question: question ?? this.question,
      answer: answer ?? this.answer,
      comments: comments ?? this.comments,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'answer': answer,
      'comments': comments,
    };
  }

  static Map<String, dynamic> toJson(List<Question> questions) {
    return {
      'list_questions': questions.map((q) => q.toMap()).toList(),
    };
  }

  factory Question.fromMap(Map<String, dynamic> map) {
    return Question(
      id: map['id']?.toInt() ?? 0,
      question: map['question'] ?? '',
      answer: map['answer'] as bool?,
      comments: map['comments'] as String?,
    );
  }

  factory Question.fromJson(String source) =>
      Question.fromMap(json.decode(source));
}
