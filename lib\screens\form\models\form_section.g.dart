// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'form_section.dart';

// **************************************************************************
// CopyWithGenerator
// **************************************************************************

abstract class _$FormSectionCWProxy {
  /// This function **does support** nullification of nullable fields. All `null` values passed to `non-nullable` fields will be ignored.
  ///
  /// Usage
  /// ```dart
  /// FormSection(...).copyWith(id: 12, name: "My name")
  /// ````
  FormSection call({
    String title,
    List<FormFieldModel> fields,
    String? description,
    List<String>? requiredFields,
  });
}

/// Proxy class for `copyWith` functionality. This is a callable class and can be used as follows: `instanceOfFormSection.copyWith(...)`.
class _$FormSectionCWProxyImpl implements _$FormSectionCWProxy {
  const _$FormSectionCWProxyImpl(this._value);

  final FormSection _value;

  @override

  /// This function **does support** nullification of nullable fields. All `null` values passed to `non-nullable` fields will be ignored.
  ///
  /// Usage
  /// ```dart
  /// FormSection(...).copyWith(id: 12, name: "My name")
  /// ````
  FormSection call({
    Object? title = const $CopyWithPlaceholder(),
    Object? fields = const $CopyWithPlaceholder(),
    Object? description = const $CopyWithPlaceholder(),
    Object? requiredFields = const $CopyWithPlaceholder(),
  }) {
    return FormSection(
      title: title == const $CopyWithPlaceholder()
          ? _value.title
          // ignore: cast_nullable_to_non_nullable
          : title as String,
      fields: fields == const $CopyWithPlaceholder()
          ? _value.fields
          // ignore: cast_nullable_to_non_nullable
          : fields as List<FormFieldModel>,
      description: description == const $CopyWithPlaceholder()
          ? _value.description
          // ignore: cast_nullable_to_non_nullable
          : description as String?,
      requiredFields: requiredFields == const $CopyWithPlaceholder()
          ? _value.requiredFields
          // ignore: cast_nullable_to_non_nullable
          : requiredFields as List<String>?,
    );
  }
}

extension $FormSectionCopyWith on FormSection {
  /// Returns a callable class that can be used as follows: `instanceOfFormSection.copyWith(...)`.
  // ignore: library_private_types_in_public_api
  _$FormSectionCWProxy get copyWith => _$FormSectionCWProxyImpl(this);

  /// Copies the object with the specific fields set to `null`. If you pass `false` as a parameter, nothing will be done and it will be ignored. Don't do it. Prefer `copyWith(field: null)`.
  ///
  /// Usage
  /// ```dart
  /// FormSection(...).copyWithNull(firstField: true, secondField: true)
  /// ````
  FormSection copyWithNull({
    bool description = false,
    bool requiredFields = false,
  }) {
    return FormSection(
      title: title,
      fields: fields,
      description: description == true ? null : this.description,
      requiredFields: requiredFields == true ? null : this.requiredFields,
    );
  }
}
