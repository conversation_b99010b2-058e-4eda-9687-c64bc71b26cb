import 'package:city_academy/core/util/app_texts.dart';
import 'package:city_academy/core/util/global_instances.dart';
import 'package:city_academy/core/widgets/appbar_custom.dart';
import 'package:city_academy/core/widgets/buttons/custom_button.dart';
import 'package:city_academy/core/widgets/buttons/info_menu_button.dart';
import 'package:city_academy/core/widgets/custom_divider.dart';
import 'package:city_academy/core/widgets/profile_tile.dart';
import 'package:city_academy/core/widgets/snack_bar.dart';
import 'package:city_academy/core/widgets/textfields/text_field_custom.dart';
import 'package:city_academy/routes/app_routes.dart';
import 'package:city_academy/routes/routes.dart';
import 'package:city_academy/screens/form/controller/cubit/form_state.dart';
import 'package:city_academy/screens/form/models/form_data.dart';
import 'package:city_academy/screens/student_details/controller/bloc/student_details_state.dart';
import 'package:city_academy/theme/colors.dart';
import 'package:city_academy/theme/texts.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class MonitoringHasDmScreen extends StatefulWidget {
  final bool isReadOnly;

  const MonitoringHasDmScreen({super.key, required this.isReadOnly});

  @override
  State<MonitoringHasDmScreen> createState() => _MonitoringHasDmScreenState();
}

class _MonitoringHasDmScreenState extends State<MonitoringHasDmScreen> {
  @override
  void initState() {
    super.initState();
    _loadFormData();
  }

  Future<void> _loadFormData() async {
    await formCubit.loadFormData(file: AppTexts.monitoringFormId);
  }

  Future<void> _navigateToForm(
    BuildContext context,
    String sectionTitle,
  ) async {
    FormData? formData;

    if (formCubit.state.globalFormData != null &&
        formCubit.state.file == AppTexts.monitoringFormId) {
      formData = formCubit.state.globalFormData ?? formCubit.state.formData;
    } else {
      await formCubit.loadFormData(file: AppTexts.monitoringFormId);
      formData = formCubit.state.formData;
    }

    if (formData == null) {
      debugPrint('Form data is null');
      return;
    }

    final section = formData.sections.firstWhere(
      (s) => s.title == sectionTitle,
      orElse: () => throw Exception('Section not found: $sectionTitle'),
    );

    router.push(
      Routes.form,
      extra: {
        'title': section.title,
        'description': section.description,
        'sections': [section.toJson()],
        'readOnly': widget.isReadOnly,
      },
    ).then((_) async => await formCubit.setFormData(
        formCubit.state.globalFormData?.toJson() ?? formData!.toJson()));
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: const AppbarCustom(title: 'Monitoramento HAS e DM'),
      body: BlocBuilder(
        bloc: formCubit,
        builder: (context, FormScreenState state) {
          if (state.isLoading) {
            return const Center(child: CircularProgressIndicator());
          }

          if (state.formData == null) {
            return const Center(child: Text('Nenhum dado disponível'));
          }

          final section = state.globalFormData!.sections.firstWhere(
            (s) => s.title == "AFERIÇÃO",
          );

          final systolicField =
              section.fields.firstWhere((f) => f.name == "systolic");

          final systolicValue = state.formValues["systolic"] ?? '';
          final diastolicValue = state.formValues["diastolic"] ?? '';
          final value = state.formValues["blood_glucose"] ?? '';

          return SingleChildScrollView(
            padding: const EdgeInsets.symmetric(horizontal: 16.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const SizedBox(height: 26),
                BlocBuilder(
                  bloc: studentDetailsBloc,
                  builder: (context, StudentDetailsState state) {
                    return ProfileTile(user: state.student);
                  },
                ),
                const SizedBox(height: 26),
                const CustomDivider(),
                SectionButton(
                  formData: state.formData!,
                  sectionTitle: section.title,
                  buttonTitle: 'Medicamentos *',
                  onTap: _navigateToForm,
                ),
                const CustomDivider(),
                TextFieldCustom(
                  label: systolicField.label,
                  initialValue: systolicValue.toString(),
                  onChanged: (value) =>
                      formCubit.updateFieldValue("systolic", value),
                ),
                const SizedBox(height: 16),
                Text(
                  'PA sistólica/distólica (mmHg)',
                  style: titleText18.copyWith(
                    color: CustomColors.neutralDark900,
                    fontWeight: FontWeight.w500,
                  ),
                ),
                const SizedBox(height: 8),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Expanded(
                      flex: 2,
                      child: TextFieldCustom(
                        initialValue: diastolicValue.toString(),
                        onChanged: (value) =>
                            formCubit.updateFieldValue("diastolic", value),
                      ),
                    ),
                    const Expanded(
                        child: Center(child: Text('/', style: titleText20))),
                    Expanded(
                      flex: 2,
                      child: TextFieldCustom(
                        initialValue: value.toString(),
                        onChanged: (value) =>
                            formCubit.updateFieldValue("blood_glucose", value),
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 16),
                if (!widget.isReadOnly) ...[
                  const SizedBox(height: 24),
                  CustomButton(
                    title: 'Finalizar Monitoramento',
                    onPressed: () async {
                      try {
                        if (systolicValue.toString().trim().isEmpty ||
                            diastolicValue.toString().trim().isEmpty ||
                            value.toString().trim().isEmpty) {
                          showErrorSnackbar(
                            context,
                            'Preencha todos os campos de aferição',
                          );
                          return;
                        }

                        if (formCubit.validateForm()) {
                          await formCubit.saveForm(isFinishing: true);
                          if (!context.mounted) return;
                          showCustomSnackbar(
                            context,
                            'Formulário salvo com sucesso',
                          );
                          await studentDetailsBloc.refreshStudentData();
                          router.pop();
                          router.pop();
                        } else {
                          showErrorSnackbar(
                            context,
                            'Preencha todos os campos obrigatórios',
                          );
                        }
                      } catch (e) {
                        if (!context.mounted) return;
                        showErrorSnackbar(
                          context,
                          'Erro ao salvar formulário: ${e.toString()}',
                        );
                      }
                    },
                  ),
                ],
                const SizedBox(height: 24),
              ],
            ),
          );
        },
      ),
    );
  }
}
