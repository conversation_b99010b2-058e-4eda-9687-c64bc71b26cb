import 'package:city_academy/core/util/formatters.dart';
import 'package:city_academy/core/widgets/textfields/text_field_custom.dart';
import 'package:flutter/services.dart';

class NameTextField extends TextFieldCustom {
  NameTextField({
    super.key,
    super.onChanged,
    super.errorMessage,
    super.controller,
    super.hintText = 'Nome Sobrenome',
    super.isEnabled,
    super.textInputAction,
    super.validator,
  }) : super(
          keyboardType: TextInputType.text,
          inputFormatters: [Formatters.nameFormatter],
        );
}
