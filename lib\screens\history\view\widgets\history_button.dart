import 'package:city_academy/theme/colors.dart';
import 'package:city_academy/theme/texts.dart';
import 'package:flutter/material.dart';

class HistoryButton extends StatelessWidget {
  final String title;
  final String? subTitle;
  final bool isEdit;
  final Function()? onTap;

  const HistoryButton({
    super.key,
    required this.title,
    this.subTitle,
    this.isEdit = false,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 6,
      color: CustomColors.neutralWhite,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      child: Container(
        decoration: BoxDecoration(
          border: Border.all(color: CustomColors.neutralGray200),
          borderRadius: BorderRadius.circular(16),
        ),
        child: InkWell(
          onTap: onTap,
          child: Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
            child: Row(
              children: [
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        title,
                        style: titleText16.copyWith(
                          color: CustomColors.neutralDark700,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                      if (subTitle != null)
                        Text(
                          subTitle!,
                          style: titleText12.copyWith(
                            color: CustomColors.neutralDark700,
                            fontWeight: FontWeight.w300,
                          ),
                        ),
                    ],
                  ),
                ),
                const SizedBox(width: 16),
                Text(
                  isEdit ? 'Continuar' : 'Ver detalhes',
                  style: titleText12.copyWith(
                    color: CustomColors.greenDark,
                    fontWeight: FontWeight.w300,
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
