class MeasurementData {
  final DateTime date;
  final double? bloodGlucose;
  final double? systolicPressure;
  final double? diastolicPressure;

  MeasurementData({
    required this.date,
    this.bloodGlucose,
    this.systolicPressure,
    this.diastolicPressure,
  });

  factory MeasurementData.fromJson(Map<String, dynamic> json) {
    return MeasurementData(
      date: DateTime.parse(json['date']),
      bloodGlucose: json['blood_glucose'] != null 
          ? double.tryParse(json['blood_glucose'].toString()) 
          : null,
      systolicPressure: json['systolic_pressure'] != null 
          ? double.tryParse(json['systolic_pressure'].toString()) 
          : null,
      diastolicPressure: json['diastolic_pressure'] != null 
          ? double.tryParse(json['diastolic_pressure'].toString()) 
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'date': date.toIso8601String(),
      'blood_glucose': bloodGlucose,
      'systolic_pressure': systolicPressure,
      'diastolic_pressure': diastolicPressure,
    };
  }
}