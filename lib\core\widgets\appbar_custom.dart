import 'package:city_academy/theme/colors.dart';
import 'package:city_academy/theme/texts.dart';
import 'package:flutter/material.dart';

class AppbarCustom extends StatelessWidget implements PreferredSizeWidget {
  final String title;
  const AppbarCustom({super.key, required this.title});

  @override
  Widget build(BuildContext context) {
    final bool canPop = (ModalRoute.of(context)?.canPop ?? false) &&
        !Scaffold.of(context).isEndDrawerOpen;

    return AppBar(
      backgroundColor: CustomColors.greenDark,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.only(
          bottomLeft: Radius.circular(10),
          bottomRight: Radius.circular(10),
        ),
      ),
      elevation: 4,
      leading: canPop
          ? IconButton(
              icon: const Icon(
                Icons.arrow_back_ios,
                color: Colors.white,
                size: 18,
              ),
              onPressed: () {
                Navigator.of(context).pop();
              },
            )
          : null,
      title: Text(
        title,
        overflow: TextOverflow.ellipsis,
        style: heading4.copyWith(
          fontWeight: FontWeight.w500,
          color: CustomColors.neutralWhite,
        ),
      ),
      actions: [
        if (!canPop)
          IconButton(
            icon: const Icon(
              Icons.menu,
              color: Colors.white,
              size: 28,
            ),
            onPressed: () {
              Scaffold.of(context).openEndDrawer();
            },
          )
      ],
    );
  }

  @override
  Size get preferredSize => const Size.fromHeight(70);
}
