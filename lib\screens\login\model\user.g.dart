// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'user.dart';

// **************************************************************************
// CopyWithGenerator
// **************************************************************************

abstract class _$UserCWProxy {
  /// This function **does support** nullification of nullable fields. All `null` values passed to `non-nullable` fields will be ignored.
  ///
  /// Usage
  /// ```dart
  /// User(...).copyWith(id: 12, name: "My name")
  /// ````
  User call({
    String id,
    String sub,
    String type,
    bool active,
    DateTime createdAt,
    DateTime updatedAt,
    String profileImageId,
    String firstName,
    String lastName,
    String nickname,
    String email,
    String phone,
    UserGender gender,
    String genderIdentity,
    String race,
    String sexualOrientation,
    DateTime birthDate,
    Map<String, dynamic>? address,
    List<dynamic>? contacts,
    String? activityCenterId,
    String? document,
    String? cns,
    String? cref,
  });
}

/// Proxy class for `copyWith` functionality. This is a callable class and can be used as follows: `instanceOfUser.copyWith(...)`.
class _$UserCWProxyImpl implements _$UserCWProxy {
  const _$UserCWProxyImpl(this._value);

  final User _value;

  @override

  /// This function **does support** nullification of nullable fields. All `null` values passed to `non-nullable` fields will be ignored.
  ///
  /// Usage
  /// ```dart
  /// User(...).copyWith(id: 12, name: "My name")
  /// ````
  User call({
    Object? id = const $CopyWithPlaceholder(),
    Object? sub = const $CopyWithPlaceholder(),
    Object? type = const $CopyWithPlaceholder(),
    Object? active = const $CopyWithPlaceholder(),
    Object? createdAt = const $CopyWithPlaceholder(),
    Object? updatedAt = const $CopyWithPlaceholder(),
    Object? profileImageId = const $CopyWithPlaceholder(),
    Object? firstName = const $CopyWithPlaceholder(),
    Object? lastName = const $CopyWithPlaceholder(),
    Object? nickname = const $CopyWithPlaceholder(),
    Object? email = const $CopyWithPlaceholder(),
    Object? phone = const $CopyWithPlaceholder(),
    Object? gender = const $CopyWithPlaceholder(),
    Object? genderIdentity = const $CopyWithPlaceholder(),
    Object? race = const $CopyWithPlaceholder(),
    Object? sexualOrientation = const $CopyWithPlaceholder(),
    Object? birthDate = const $CopyWithPlaceholder(),
    Object? address = const $CopyWithPlaceholder(),
    Object? contacts = const $CopyWithPlaceholder(),
    Object? activityCenterId = const $CopyWithPlaceholder(),
    Object? document = const $CopyWithPlaceholder(),
    Object? cns = const $CopyWithPlaceholder(),
    Object? cref = const $CopyWithPlaceholder(),
  }) {
    return User(
      id: id == const $CopyWithPlaceholder()
          ? _value.id
          // ignore: cast_nullable_to_non_nullable
          : id as String,
      sub: sub == const $CopyWithPlaceholder()
          ? _value.sub
          // ignore: cast_nullable_to_non_nullable
          : sub as String,
      type: type == const $CopyWithPlaceholder()
          ? _value.type
          // ignore: cast_nullable_to_non_nullable
          : type as String,
      active: active == const $CopyWithPlaceholder()
          ? _value.active
          // ignore: cast_nullable_to_non_nullable
          : active as bool,
      createdAt: createdAt == const $CopyWithPlaceholder()
          ? _value.createdAt
          // ignore: cast_nullable_to_non_nullable
          : createdAt as DateTime,
      updatedAt: updatedAt == const $CopyWithPlaceholder()
          ? _value.updatedAt
          // ignore: cast_nullable_to_non_nullable
          : updatedAt as DateTime,
      profileImageId: profileImageId == const $CopyWithPlaceholder()
          ? _value.profileImageId
          // ignore: cast_nullable_to_non_nullable
          : profileImageId as String,
      firstName: firstName == const $CopyWithPlaceholder()
          ? _value.firstName
          // ignore: cast_nullable_to_non_nullable
          : firstName as String,
      lastName: lastName == const $CopyWithPlaceholder()
          ? _value.lastName
          // ignore: cast_nullable_to_non_nullable
          : lastName as String,
      nickname: nickname == const $CopyWithPlaceholder()
          ? _value.nickname
          // ignore: cast_nullable_to_non_nullable
          : nickname as String,
      email: email == const $CopyWithPlaceholder()
          ? _value.email
          // ignore: cast_nullable_to_non_nullable
          : email as String,
      phone: phone == const $CopyWithPlaceholder()
          ? _value.phone
          // ignore: cast_nullable_to_non_nullable
          : phone as String,
      gender: gender == const $CopyWithPlaceholder()
          ? _value.gender
          // ignore: cast_nullable_to_non_nullable
          : gender as UserGender,
      genderIdentity: genderIdentity == const $CopyWithPlaceholder()
          ? _value.genderIdentity
          // ignore: cast_nullable_to_non_nullable
          : genderIdentity as String,
      race: race == const $CopyWithPlaceholder()
          ? _value.race
          // ignore: cast_nullable_to_non_nullable
          : race as String,
      sexualOrientation: sexualOrientation == const $CopyWithPlaceholder()
          ? _value.sexualOrientation
          // ignore: cast_nullable_to_non_nullable
          : sexualOrientation as String,
      birthDate: birthDate == const $CopyWithPlaceholder()
          ? _value.birthDate
          // ignore: cast_nullable_to_non_nullable
          : birthDate as DateTime,
      address: address == const $CopyWithPlaceholder()
          ? _value.address
          // ignore: cast_nullable_to_non_nullable
          : address as Map<String, dynamic>?,
      contacts: contacts == const $CopyWithPlaceholder()
          ? _value.contacts
          // ignore: cast_nullable_to_non_nullable
          : contacts as List<dynamic>?,
      activityCenterId: activityCenterId == const $CopyWithPlaceholder()
          ? _value.activityCenterId
          // ignore: cast_nullable_to_non_nullable
          : activityCenterId as String?,
      document: document == const $CopyWithPlaceholder()
          ? _value.document
          // ignore: cast_nullable_to_non_nullable
          : document as String?,
      cns: cns == const $CopyWithPlaceholder()
          ? _value.cns
          // ignore: cast_nullable_to_non_nullable
          : cns as String?,
      cref: cref == const $CopyWithPlaceholder()
          ? _value.cref
          // ignore: cast_nullable_to_non_nullable
          : cref as String?,
    );
  }
}

extension $UserCopyWith on User {
  /// Returns a callable class that can be used as follows: `instanceOfUser.copyWith(...)`.
  // ignore: library_private_types_in_public_api
  _$UserCWProxy get copyWith => _$UserCWProxyImpl(this);

  /// Copies the object with the specific fields set to `null`. If you pass `false` as a parameter, nothing will be done and it will be ignored. Don't do it. Prefer `copyWith(field: null)`.
  ///
  /// Usage
  /// ```dart
  /// User(...).copyWithNull(firstField: true, secondField: true)
  /// ````
  User copyWithNull({
    bool address = false,
    bool contacts = false,
    bool activityCenterId = false,
    bool document = false,
    bool cns = false,
    bool cref = false,
  }) {
    return User(
      id: id,
      sub: sub,
      type: type,
      active: active,
      createdAt: createdAt,
      updatedAt: updatedAt,
      profileImageId: profileImageId,
      firstName: firstName,
      lastName: lastName,
      nickname: nickname,
      email: email,
      phone: phone,
      gender: gender,
      genderIdentity: genderIdentity,
      race: race,
      sexualOrientation: sexualOrientation,
      birthDate: birthDate,
      address: address == true ? null : this.address,
      contacts: contacts == true ? null : this.contacts,
      activityCenterId: activityCenterId == true ? null : this.activityCenterId,
      document: document == true ? null : this.document,
      cns: cns == true ? null : this.cns,
      cref: cref == true ? null : this.cref,
    );
  }
}

// **************************************************************************
// EmptyGenerator
// **************************************************************************

class UserEmpty extends User {
  UserEmpty()
      : super(
            id: '',
            sub: '',
            type: '',
            active: false,
            createdAt: DateTime.now(),
            updatedAt: DateTime.now(),
            profileImageId: '',
            firstName: '',
            lastName: '',
            nickname: '',
            email: '',
            phone: '',
            gender: UserGender.values.first,
            genderIdentity: '',
            race: '',
            sexualOrientation: '',
            birthDate: DateTime.now(),
            address: null,
            contacts: null,
            activityCenterId: null,
            document: null,
            cns: null,
            cref: null);
}
