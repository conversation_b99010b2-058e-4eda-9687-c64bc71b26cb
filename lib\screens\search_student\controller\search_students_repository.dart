import 'package:city_academy/core/util/app_texts.dart';
import 'package:city_academy/core/util/enums.dart';
import 'package:city_academy/core/util/global_instances.dart';
import 'package:city_academy/screens/student_details/model/user_in_activity.dart';

class SearchStudentsRepository {
  SearchStudentsRepository();

  Future<List<UserInActivity>> searchStudents(
    SearchType type,
    String query,
  ) async {
    final trimmedQuery = query.trim();

    final body = <String, String>{};

    if (trimmedQuery.isNotEmpty) {
      if (type == SearchType.cpf) {
        body['document'] = trimmedQuery;
      } else {
        body['first_name'] = trimmedQuery;
      }
    }

    final response = await authClient.get(
      path: AppTexts.listTrainees,
      query: body,
    );

    return (response.data['items'] as List)
        .map((e) => UserInActivity.fromJson(e))
        .toList();
  }
}
