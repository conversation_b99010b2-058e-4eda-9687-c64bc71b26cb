import 'package:city_academy/core/util/global_instances.dart';
import 'package:city_academy/core/util/image_path.dart';
import 'package:city_academy/routes/app_routes.dart';
import 'package:city_academy/routes/routes.dart';
import 'package:city_academy/theme/colors.dart';
import 'package:flutter/material.dart';

class SplashScreen extends StatefulWidget {
  const SplashScreen({super.key});

  @override
  State<SplashScreen> createState() => _SplashScreenState();
}

class _SplashScreenState extends State<SplashScreen> {
  @override
  void initState() {
    super.initState();
    _checkAuthentication();
  }

  Future<void> _checkAuthentication() async {
    try {
      await Future.delayed(const Duration(milliseconds: 1500));

      // Inicializa o estado de autenticação baseado nos tokens
      await authService.initializeAuthState();

      // Faz verificação rápida (sem requisição se já tem tokens)
      final isAuthenticated = await authService.checkAuthentication();

      if (mounted) {
        if (isAuthenticated) {
          await loginCubit.getCoachInfo();
          router.go(Routes.home);
        } else {
          router.go(Routes.login);
        }
      }
    } catch (e) {
      if (mounted) {
        router.go(Routes.login);
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: CustomColors.neutralWhite,
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Image.asset(
              ImagePath.logo,
              height: 150,
            ),
            const SizedBox(height: 40),
            const CircularProgressIndicator(
              color: CustomColors.greenBase,
            ),
          ],
        ),
      ),
    );
  }
}
