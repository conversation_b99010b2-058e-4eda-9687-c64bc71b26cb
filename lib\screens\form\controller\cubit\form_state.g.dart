// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'form_state.dart';

// **************************************************************************
// CopyWithGenerator
// **************************************************************************

abstract class _$FormScreenStateCWProxy {
  /// This function **does support** nullification of nullable fields. All `null` values passed to `non-nullable` fields will be ignored.
  ///
  /// Usage
  /// ```dart
  /// FormScreenState(...).copyWith(id: 12, name: "My name")
  /// ````
  FormScreenState call({
    bool isLoading,
    Map<String, dynamic> formValues,
    Map<String, bool> checkboxValues,
    Map<String, String> booleanSelections,
    bool isHealthUnitEnabled,
    List<dynamic> healthUnitOptions,
    UserInActivity user,
    FormData? globalFormData,
    String? file,
    FormData? formData,
    String? selectedDistrict,
    List<FormData>? formResponses,
    String? responseId,
    List<Coach>? coachs,
  });
}

/// Proxy class for `copyWith` functionality. This is a callable class and can be used as follows: `instanceOfFormScreenState.copyWith(...)`.
class _$FormScreenStateCWProxyImpl implements _$FormScreenStateCWProxy {
  const _$FormScreenStateCWProxyImpl(this._value);

  final FormScreenState _value;

  @override

  /// This function **does support** nullification of nullable fields. All `null` values passed to `non-nullable` fields will be ignored.
  ///
  /// Usage
  /// ```dart
  /// FormScreenState(...).copyWith(id: 12, name: "My name")
  /// ````
  FormScreenState call({
    Object? isLoading = const $CopyWithPlaceholder(),
    Object? formValues = const $CopyWithPlaceholder(),
    Object? checkboxValues = const $CopyWithPlaceholder(),
    Object? booleanSelections = const $CopyWithPlaceholder(),
    Object? isHealthUnitEnabled = const $CopyWithPlaceholder(),
    Object? healthUnitOptions = const $CopyWithPlaceholder(),
    Object? user = const $CopyWithPlaceholder(),
    Object? globalFormData = const $CopyWithPlaceholder(),
    Object? file = const $CopyWithPlaceholder(),
    Object? formData = const $CopyWithPlaceholder(),
    Object? selectedDistrict = const $CopyWithPlaceholder(),
    Object? formResponses = const $CopyWithPlaceholder(),
    Object? responseId = const $CopyWithPlaceholder(),
    Object? coachs = const $CopyWithPlaceholder(),
  }) {
    return FormScreenState(
      isLoading: isLoading == const $CopyWithPlaceholder()
          ? _value.isLoading
          // ignore: cast_nullable_to_non_nullable
          : isLoading as bool,
      formValues: formValues == const $CopyWithPlaceholder()
          ? _value.formValues
          // ignore: cast_nullable_to_non_nullable
          : formValues as Map<String, dynamic>,
      checkboxValues: checkboxValues == const $CopyWithPlaceholder()
          ? _value.checkboxValues
          // ignore: cast_nullable_to_non_nullable
          : checkboxValues as Map<String, bool>,
      booleanSelections: booleanSelections == const $CopyWithPlaceholder()
          ? _value.booleanSelections
          // ignore: cast_nullable_to_non_nullable
          : booleanSelections as Map<String, String>,
      isHealthUnitEnabled: isHealthUnitEnabled == const $CopyWithPlaceholder()
          ? _value.isHealthUnitEnabled
          // ignore: cast_nullable_to_non_nullable
          : isHealthUnitEnabled as bool,
      healthUnitOptions: healthUnitOptions == const $CopyWithPlaceholder()
          ? _value.healthUnitOptions
          // ignore: cast_nullable_to_non_nullable
          : healthUnitOptions as List<dynamic>,
      user: user == const $CopyWithPlaceholder()
          ? _value.user
          // ignore: cast_nullable_to_non_nullable
          : user as UserInActivity,
      globalFormData: globalFormData == const $CopyWithPlaceholder()
          ? _value.globalFormData
          // ignore: cast_nullable_to_non_nullable
          : globalFormData as FormData?,
      file: file == const $CopyWithPlaceholder()
          ? _value.file
          // ignore: cast_nullable_to_non_nullable
          : file as String?,
      formData: formData == const $CopyWithPlaceholder()
          ? _value.formData
          // ignore: cast_nullable_to_non_nullable
          : formData as FormData?,
      selectedDistrict: selectedDistrict == const $CopyWithPlaceholder()
          ? _value.selectedDistrict
          // ignore: cast_nullable_to_non_nullable
          : selectedDistrict as String?,
      formResponses: formResponses == const $CopyWithPlaceholder()
          ? _value.formResponses
          // ignore: cast_nullable_to_non_nullable
          : formResponses as List<FormData>?,
      responseId: responseId == const $CopyWithPlaceholder()
          ? _value.responseId
          // ignore: cast_nullable_to_non_nullable
          : responseId as String?,
      coachs: coachs == const $CopyWithPlaceholder()
          ? _value.coachs
          // ignore: cast_nullable_to_non_nullable
          : coachs as List<Coach>?,
    );
  }
}

extension $FormScreenStateCopyWith on FormScreenState {
  /// Returns a callable class that can be used as follows: `instanceOfFormScreenState.copyWith(...)`.
  // ignore: library_private_types_in_public_api
  _$FormScreenStateCWProxy get copyWith => _$FormScreenStateCWProxyImpl(this);

  /// Copies the object with the specific fields set to `null`. If you pass `false` as a parameter, nothing will be done and it will be ignored. Don't do it. Prefer `copyWith(field: null)`.
  ///
  /// Usage
  /// ```dart
  /// FormScreenState(...).copyWithNull(firstField: true, secondField: true)
  /// ````
  FormScreenState copyWithNull({
    bool globalFormData = false,
    bool file = false,
    bool formData = false,
    bool selectedDistrict = false,
    bool formResponses = false,
    bool responseId = false,
    bool coachs = false,
  }) {
    return FormScreenState(
      isLoading: isLoading,
      formValues: formValues,
      checkboxValues: checkboxValues,
      booleanSelections: booleanSelections,
      isHealthUnitEnabled: isHealthUnitEnabled,
      healthUnitOptions: healthUnitOptions,
      user: user,
      globalFormData: globalFormData == true ? null : this.globalFormData,
      file: file == true ? null : this.file,
      formData: formData == true ? null : this.formData,
      selectedDistrict: selectedDistrict == true ? null : this.selectedDistrict,
      formResponses: formResponses == true ? null : this.formResponses,
      responseId: responseId == true ? null : this.responseId,
      coachs: coachs == true ? null : this.coachs,
    );
  }
}

// **************************************************************************
// EmptyGenerator
// **************************************************************************

class FormScreenStateEmpty extends FormScreenState {
  FormScreenStateEmpty()
      : super(
            isLoading: false,
            formResponses: null,
            formData: null,
            globalFormData: null,
            formValues: const {},
            checkboxValues: const {},
            booleanSelections: const {},
            selectedDistrict: null,
            file: null,
            healthUnitOptions: const [],
            isHealthUnitEnabled: false,
            user: UserInActivityEmpty(),
            responseId: null,
            coachs: null);
}
