import 'package:city_academy/theme/colors.dart';
import 'package:city_academy/theme/texts.dart';
import 'package:flutter/material.dart';

class InfoColumn extends Column {
  final String title;
  final String value;

  InfoColumn({super.key, required this.title, required this.value})
      : super(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              title,
              style: titleText18.copyWith(
                fontWeight: FontWeight.w500,
                color: CustomColors.neutralDark900,
              ),
            ),
            Text(
              value,
              style: titleText16.copyWith(
                color: CustomColors.neutralGray500,
              ),
            ),
          ],
        );
}
