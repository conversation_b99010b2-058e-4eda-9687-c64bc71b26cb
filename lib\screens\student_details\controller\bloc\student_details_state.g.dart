// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'student_details_state.dart';

// **************************************************************************
// CopyWithGenerator
// **************************************************************************

abstract class _$StudentDetailsStateCWProxy {
  /// This function **does support** nullification of nullable fields. All `null` values passed to `non-nullable` fields will be ignored.
  ///
  /// Usage
  /// ```dart
  /// StudentDetailsState(...).copyWith(id: 12, name: "My name")
  /// ````
  StudentDetailsState call({
    UserInActivity student,
    String? error,
    bool loading,
  });
}

/// Proxy class for `copyWith` functionality. This is a callable class and can be used as follows: `instanceOfStudentDetailsState.copyWith(...)`.
class _$StudentDetailsStateCWProxyImpl implements _$StudentDetailsStateCWProxy {
  const _$StudentDetailsStateCWProxyImpl(this._value);

  final StudentDetailsState _value;

  @override

  /// This function **does support** nullification of nullable fields. All `null` values passed to `non-nullable` fields will be ignored.
  ///
  /// Usage
  /// ```dart
  /// StudentDetailsState(...).copyWith(id: 12, name: "My name")
  /// ````
  StudentDetailsState call({
    Object? student = const $CopyWithPlaceholder(),
    Object? error = const $CopyWithPlaceholder(),
    Object? loading = const $CopyWithPlaceholder(),
  }) {
    return StudentDetailsState(
      student: student == const $CopyWithPlaceholder()
          ? _value.student
          // ignore: cast_nullable_to_non_nullable
          : student as UserInActivity,
      error: error == const $CopyWithPlaceholder()
          ? _value.error
          // ignore: cast_nullable_to_non_nullable
          : error as String?,
      loading: loading == const $CopyWithPlaceholder()
          ? _value.loading
          // ignore: cast_nullable_to_non_nullable
          : loading as bool,
    );
  }
}

extension $StudentDetailsStateCopyWith on StudentDetailsState {
  /// Returns a callable class that can be used as follows: `instanceOfStudentDetailsState.copyWith(...)`.
  // ignore: library_private_types_in_public_api
  _$StudentDetailsStateCWProxy get copyWith =>
      _$StudentDetailsStateCWProxyImpl(this);

  /// Copies the object with the specific fields set to `null`. If you pass `false` as a parameter, nothing will be done and it will be ignored. Don't do it. Prefer `copyWith(field: null)`.
  ///
  /// Usage
  /// ```dart
  /// StudentDetailsState(...).copyWithNull(firstField: true, secondField: true)
  /// ````
  StudentDetailsState copyWithNull({
    bool error = false,
  }) {
    return StudentDetailsState(
      student: student,
      error: error == true ? null : this.error,
      loading: loading,
    );
  }
}

// **************************************************************************
// EmptyGenerator
// **************************************************************************

class StudentDetailsStateEmpty extends StudentDetailsState {
  StudentDetailsStateEmpty()
      : super(student: UserInActivityEmpty(), error: null, loading: false);
}
