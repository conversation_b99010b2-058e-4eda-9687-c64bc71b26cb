import 'package:city_academy/core/util/global_instances.dart';
import 'package:city_academy/core/widgets/textfields/text_field_custom.dart';
import 'package:city_academy/screens/form/models/form_field_model.dart';
import 'package:city_academy/theme/colors.dart';
import 'package:city_academy/theme/texts.dart';
import 'package:flutter/material.dart';

Widget buildTextField(FormFieldModel field, bool isReadyOnly) {
  if (field.name == 'systolic_pa' || field.name == 'diastolic_pa') {
    return _buildBloodPressureFields(field, isReadyOnly);
  }

  final currentValue = formCubit.state.formValues[field.name]?.toString() ?? '';
  final canEdit = !isReadyOnly && field.canEdit;
  final controller = TextEditingController(text: currentValue);

  controller.selection = TextSelection.fromPosition(
    TextPosition(offset: controller.text.length),
  );

  return Padding(
    padding: const EdgeInsets.only(bottom: 16.0),
    child: TextFieldCustom(
      isEnabled: canEdit,
      label: field.label,
      controller: controller,
      onChanged: !canEdit
          ? null
          : (value) => formCubit.updateFieldValue(field.name, value),
    ),
  );
}

Widget _buildBloodPressureFields(FormFieldModel field, bool isReadOnly) {
  final systolicValue =
      formCubit.state.formValues['systolic_pa']?.toString() ?? '';
  final diastolicValue =
      formCubit.state.formValues['diastolic_pa']?.toString() ?? '';

  final canEdit = !isReadOnly && field.canEdit;

  final systolicController = TextEditingController(text: systolicValue);
  final diastolicController = TextEditingController(text: diastolicValue);

  systolicController.selection = TextSelection.fromPosition(
    TextPosition(offset: systolicController.text.length),
  );
  diastolicController.selection = TextSelection.fromPosition(
    TextPosition(offset: diastolicController.text.length),
  );

  return Padding(
    padding: const EdgeInsets.only(bottom: 16.0),
    child: Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'PA sistólica/distólica (mmHg)',
          style: titleText18.copyWith(
            color: CustomColors.neutralDark900,
            fontWeight: FontWeight.w500,
          ),
        ),
        const SizedBox(height: 8),
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Expanded(
              flex: 2,
              child: TextFieldCustom(
                isEnabled: canEdit,
                controller: systolicController,
                onChanged: !canEdit
                    ? null
                    : (value) =>
                        formCubit.updateFieldValue('systolic_pa', value),
              ),
            ),
            const Expanded(
              child: Center(
                child: Text('/', style: titleText20),
              ),
            ),
            Expanded(
              flex: 2,
              child: TextFieldCustom(
                isEnabled: canEdit,
                controller: diastolicController,
                onChanged: !canEdit
                    ? null
                    : (value) =>
                        formCubit.updateFieldValue('diastolic_pa', value),
              ),
            ),
          ],
        ),
      ],
    ),
  );
}
