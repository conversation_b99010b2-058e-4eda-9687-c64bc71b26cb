class LoginResponseModel {
  final String localId;
  final String email;
  final String displayName;
  final String idToken;
  final bool registered;
  final String refreshToken;
  final int expiresIn;

  LoginResponseModel({
    required this.localId,
    required this.email,
    required this.displayName,
    required this.idToken,
    required this.registered,
    required this.refreshToken,
    required this.expiresIn,
  });

  factory LoginResponseModel.fromJson(Map<String, dynamic> map) {
    return LoginResponseModel(
      localId: map['localId'] ?? '',
      email: map['email'] ?? '',
      displayName: map['displayName'] as String? ?? '',
      idToken: map['idToken'] ?? '',
      registered: map['registered'] ?? false,
      refreshToken: map['refreshToken'] ?? '',
      expiresIn: map['expiresIn'] ?? 0,
    );
  }
}
