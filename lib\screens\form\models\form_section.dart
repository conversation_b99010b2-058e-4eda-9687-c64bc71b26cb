import 'package:copy_with_extension/copy_with_extension.dart';

import 'form_field_model.dart';

part 'form_section.g.dart';

@CopyWith()
class FormSection {
  final String title;
  final List<FormFieldModel> fields;
  final String? description;
  final List<String>? requiredFields;

  FormSection({
    required this.title,
    required this.fields,
    this.description,
    this.requiredFields,
  });

  factory FormSection.fromJson(Map<String, dynamic> json) {
    return FormSection(
      title: json['title'] as String,
      description: json['description'] as String?,
      fields: (json['fields'] as List<dynamic>)
          .map(
              (field) => FormFieldModel.fromJson(field as Map<String, dynamic>))
          .toList(),
      requiredFields: (json['requiredFields'] as List<dynamic>? ?? [])
          .map((e) => e as String)
          .toList(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'title': title,
      if (description != null) 'description': description,
      'fields': fields.map((field) => field.toJson()).toList(),
      if (requiredFields != null && (requiredFields?.isNotEmpty ?? false))
        'requiredFields': requiredFields,
    };
  }
}
