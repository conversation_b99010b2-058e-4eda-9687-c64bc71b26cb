import 'package:city_academy/core/util/enums.dart';
import 'package:city_academy/core/util/extentions.dart';
import 'package:city_academy/core/util/functions/funcs.dart';
import 'package:city_academy/core/util/global_instances.dart';
import 'package:city_academy/routes/app_routes.dart';
import 'package:city_academy/screens/activity_list/model/activity_model.dart';
import 'package:city_academy/screens/event_city_admin/controller/event_city_admin_service.dart';
import 'package:city_academy/screens/event_city_admin/model/event_city_admin_model.dart';
import 'package:city_academy/screens/presence_form/controller/presence_form_service.dart';
import 'package:city_academy/screens/student_details/model/user_in_activity.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import 'event_city_admin_state.dart';

class EventCityAdminCubit extends Cubit<EventCityAdminState> {
  EventCityAdminCubit() : super(EventCityAdminStateEmpty());

  void fetchUsers() async {
    emit(state.copyWith(loading: true));
    try {
      List<UserInActivity> response = [];
      if (state.eventCity.type ==
          ActivityType.activity.toStringRepresentation()) {
        final dayPeriod = getDayPeriod(state.eventCity.startTime);
        response = await EventCityAdminService().fetchUsers(
          dayPeriod: dayPeriod,
        );
      }
      final response2 = await EventCityAdminService().fetchUsers(
        usersIds: state.eventCity.users,
      );

      emit(state.copyWith(
        users: [...response, ...response2],
        listSelected: state.eventCity.users,
      ));
    } catch (e) {
      emit(state.copyWith(error: e.toString()));
    } finally {
      emit(state.copyWith(loading: false));
    }
  }

  void setUserInConfirmation(String id) {
    emit(state.copyWith(listSelected: [id, ...state.listSelected]));
  }

  void addUserToConfirmation(UserInActivity user) {
    final currentUsers = List<UserInActivity>.from(state.users);
    if (!currentUsers.any((u) => u.id == user.id)) {
      currentUsers.add(user);
    }

    final currentSelected = List<String>.from(state.listSelected);
    if (!currentSelected.contains(user.id)) {
      currentSelected.add(user.id);
    }

    emit(state.copyWith(
      users: currentUsers,
      listSelected: currentSelected,
    ));
  }

  void setEventCity(EventCityAdminModel eventCity) {
    emit(state.copyWith(eventCity: eventCity));
  }

  void removeUserInConfirmation(String id) {
    final listSelected = state.listSelected;
    listSelected.remove(id);
    emit(state.copyWith(listSelected: [...listSelected]));
  }

  void deleteActivity(ActivityModel activity) async {
    emit(state.copyWith(loading: true));

    try {
      final response = await EventCityAdminService().deleteActivity(
        loginCubit.state.user.activityCenterId!,
        activity.activityId,
      );

      if (response) {
        emit(state.copyWith(status: EventCityAdminStatus.succeeds));
        router.pop();
      } else {
        emit(state.copyWith(status: EventCityAdminStatus.failed));
      }
    } catch (e) {
      emit(
        state.copyWith(
          error: e.toString(),
          status: EventCityAdminStatus.failed,
        ),
      );
    } finally {
      emit(state.copyWith(loading: false));
    }
  }

  void action(ActivityModel activity) async {
    emit(
      state.copyWith(
        loading: true,
        status: EventCityAdminStatus.none,
      ),
    );
    try {
      final updatedEventCity = state.eventCity.copyWith(
        users: state.listSelected,
      );

      await PresenceFormService().updateActivityDetails(updatedEventCity);

      emit(
        state.copyWith(
          loading: false,
          status: EventCityAdminStatus.succeeds,
        ),
      );
    } catch (e) {
      emit(
        state.copyWith(
          loading: false,
          error: e.toString(),
          status: EventCityAdminStatus.failed,
        ),
      );
    }
  }
}
