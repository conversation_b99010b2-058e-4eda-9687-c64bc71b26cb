import 'package:city_academy/core/util/extentions.dart';
import 'package:city_academy/screens/login/model/user.dart';

class Coach extends User {
  Coach({
    required super.id,
    required super.sub,
    required super.type,
    required super.active,
    required super.createdAt,
    required super.updatedAt,
    required super.profileImageId,
    required super.firstName,
    required super.lastName,
    required super.nickname,
    required super.email,
    required super.phone,
    required super.gender,
    required super.genderIdentity,
    required super.race,
    required super.sexualOrientation,
    required super.birthDate,
    required super.address,
    required super.contacts,
    required super.cns,
  });

  factory Coach.fromJson(Map<String, dynamic> json) {
    return Coach(
      id: json['id'],
      sub: json['sub'],
      type: json['type'],
      active: json['active'],
      createdAt: DateTime.parse(json['created_at']),
      updatedAt: DateTime.parse(json['updated_at']),
      profileImageId: '',
      firstName: json['first_name'],
      lastName: json['last_name'],
      nickname: json['nickname'],
      email: json['email'],
      phone: json['phone'],
      gender: (json['gender'] as String? ?? 'male').toGender(),
      genderIdentity: json['gender_identity'],
      race: json['race'],
      sexualOrientation: json['sexual_orientation'],
      birthDate: DateTime.parse(json['birth_date']),
      address: json['address'],
      contacts: json['contacts'],
      cns: json['cns'],
    );
  }
}
