import 'package:city_academy/core/util/app_texts.dart';
import 'package:city_academy/core/util/global_instances.dart';
import 'package:city_academy/core/widgets/appbar_custom.dart';
import 'package:city_academy/core/widgets/buttons/custom_button.dart';
import 'package:city_academy/core/widgets/buttons/info_menu_button.dart';
import 'package:city_academy/core/widgets/custom_divider.dart';
import 'package:city_academy/core/widgets/profile_tile.dart';
import 'package:city_academy/core/widgets/snack_bar.dart';
import 'package:city_academy/routes/app_routes.dart';
import 'package:city_academy/routes/routes.dart';
import 'package:city_academy/screens/form/controller/cubit/form_state.dart';
import 'package:city_academy/screens/form/models/form_data.dart';
import 'package:city_academy/screens/form/models/form_section.dart';
import 'package:city_academy/screens/student_details/controller/bloc/student_details_state.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class AssessmentsInfoScreen extends StatefulWidget {
  final bool isReadOnly;

  const AssessmentsInfoScreen({super.key, required this.isReadOnly});

  @override
  State<AssessmentsInfoScreen> createState() => _AssessmentsInfoScreenState();
}

class _AssessmentsInfoScreenState extends State<AssessmentsInfoScreen> {
  @override
  void initState() {
    super.initState();
    formCubit.reset();
    _loadFormData();
  }

  Future<void> _loadFormData() async {
    await formCubit.loadFormData(file: AppTexts.assessmentsFormId);
  }

  Future<void> _navigateToForm(
    BuildContext context,
    String sectionTitle,
  ) async {
    FormData? formData;

    if (formCubit.state.globalFormData != null &&
        formCubit.state.file == AppTexts.assessmentsFormId) {
      formData = formCubit.state.globalFormData ?? formCubit.state.formData;
    } else {
      await formCubit.loadFormData(file: AppTexts.assessmentsFormId);
      formData = formCubit.state.formData;
    }

    if (formData == null) {
      debugPrint('Form data is null');
      return;
    }

    final section = formData.sections.firstWhere(
      (s) => s.title == sectionTitle,
      orElse: () => throw Exception('Section not found: $sectionTitle'),
    );

    router.push(
      Routes.form,
      extra: {
        'title': section.title,
        'description': section.description,
        'sections': [section.toJson()],
        'readOnly': widget.isReadOnly,
      },
    ).then((_) async => await formCubit.setFormData(
        formCubit.state.globalFormData?.toJson() ?? formData!.toJson()));
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: const AppbarCustom(title: 'Informações de Avaliação'),
      body: BlocBuilder(
          bloc: studentDetailsBloc,
          builder: (context, StudentDetailsState state) {
            return BlocBuilder(
                bloc: formCubit,
                builder: (context, FormScreenState formState) {
                  if (formState.isLoading) {
                    return const Center(child: CircularProgressIndicator());
                  }
                  if (formState.formData == null) {
                    return const Center(child: Text('Nenhum dado disponível'));
                  }
                  final isElderly = DateTime.now()
                              .difference(state.student.birthDate)
                              .inDays ~/
                          365 >=
                      60;

                  final updatedSections =
                      List<FormSection>.from(formState.formData!.sections)
                        ..removeWhere((section) =>
                            section.title ==
                            (isElderly
                                ? 'TESTE DE APTIDÃO FÍSICA'
                                : 'TESTE DE APTIDÃO FÍSICA (IDOSOS)'));

                  final formData = formState.formData!.copyWith(
                    sections: updatedSections,
                  );

                  return SingleChildScrollView(
                    child: Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 16.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          const SizedBox(height: 26),
                          ProfileTile(user: state.student),
                          const SizedBox(height: 26),
                          const CustomDivider(),
                          ...formData.sections.map(
                            (FormSection section) {
                              return SectionButton(
                                formData: formData,
                                sectionTitle: section.title,
                                buttonTitle: section.title,
                                onTap: _navigateToForm,
                              );
                            },
                          ),
                          if (!widget.isReadOnly) ...[
                            const SizedBox(height: 24),
                            CustomButton(
                              title: 'Finalizar avaliação física',
                              onPressed: () async {
                                if (formCubit.validateForm()) {
                                  await formCubit.saveForm(isFinishing: true);
                                  if (!context.mounted) return;
                                  showCustomSnackbar(
                                      context, 'Formulário salvo com sucesso');
                                  await studentDetailsBloc.refreshStudentData();
                                  router.pop();
                                  router.pop();
                                } else {
                                  showErrorSnackbar(
                                    context,
                                    'Preencha todos os campos obrigatórios',
                                  );
                                }
                              },
                            ),
                            const SizedBox(height: 24),
                          ]
                        ],
                      ),
                    ),
                  );
                });
          }),
    );
  }
}
