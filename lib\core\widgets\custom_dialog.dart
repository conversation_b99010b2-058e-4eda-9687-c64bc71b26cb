import 'package:city_academy/core/widgets/buttons/custom_button.dart';
import 'package:city_academy/theme/colors.dart';
import 'package:city_academy/theme/texts.dart';
import 'package:flutter/material.dart';

class CustomDialog extends StatelessWidget {
  final Widget child;
  final String title;
  final void Function()? onPressed;
  final List<Widget>? actions;

  CustomDialog({
    super.key,
    required this.child,
    required this.title,
    this.onPressed,
    this.actions,
  });

  final scrollController = ScrollController();

  @override
  Widget build(BuildContext context) {
    return Dialog(
        child: IntrinsicHeight(
      child: Container(
        padding: const EdgeInsets.all(24),
        decoration: const BoxDecoration(
          color: CustomColors.neutralWhite,
          borderRadius: BorderRadius.all(
            Radius.circular(12),
          ),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            Container(
              alignment: Alignment.centerLeft,
              padding: const EdgeInsets.only(left: 12, right: 12, top: 20),
              child: Text(
                title,
                style: titleText20.copyWith(
                    fontWeight: FontWeight.w500,
                    color: CustomColors.neutralDark900),
              ),
            ),
            Expanded(
              child: Scrollbar(
                thumbVisibility: true,
                trackVisibility: true,
                interactive: true,
                controller: scrollController,
                child: SingleChildScrollView(
                  controller: scrollController,
                  child: Padding(
                    padding:
                        const EdgeInsets.symmetric(horizontal: 12, vertical: 5),
                    child: child,
                  ),
                ),
              ),
            ),
            const SizedBox(height: 12),
            if (actions != null)
              ...actions!
            else if (onPressed != null)
              CustomButton(
                title: 'Fechar',
                onPressed: onPressed,
              ),
            const SizedBox(height: 12),
          ],
        ),
      ),
    ));
  }
}
