import 'package:city_academy/core/util/dates.dart';
import 'package:city_academy/core/util/extentions.dart';
import 'package:city_academy/screens/event_city_admin/model/event_city_admin_model.dart';
import 'package:city_academy/theme/colors.dart';
import 'package:city_academy/theme/texts.dart';
import 'package:flutter/material.dart';

class ActivityTile extends StatelessWidget {
  final EventCityAdminModel eventCity;

  const ActivityTile({super.key, required this.eventCity});

  @override
  Widget build(BuildContext context) {
    return ListTile(
      leading: Column(
        mainAxisSize: MainAxisSize.min,
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Text(
            getDayByInt(
              eventCity.startTime.weekday,
              abbreviation: true,
            ),
            style: titleText10.copyWith(
              fontWeight: FontWeight.w500,
              color: CustomColors.neutralBlack,
            ),
            textAlign: TextAlign.center,
          ),
          Expanded(
            child: CircleAvatar(
              backgroundColor: CustomColors.neutralGray500,
              child: Text(
                eventCity.startTime.day.toString(),
                style: titleText12.copyWith(color: CustomColors.neutralWhite),
              ),
            ),
          )
        ],
      ),
      title: Text(
        '${eventCity.startTime.getCustomHour()} h',
        style: titleText20.copyWith(
          fontWeight: FontWeight.w500,
          color: CustomColors.neutralDark900,
        ),
      ),
      subtitle: Text(
        '${eventCity.eventName.upperOnlyFirstLetter()}${eventCity.coach != null ? ' - ${eventCity.coach!.name.nameImprovident()}' : ''}',
        style: titleText14.copyWith(
          color: CustomColors.neutralDark700,
        ),
      ),
    );
  }
}
