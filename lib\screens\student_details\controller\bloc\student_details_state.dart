import 'package:city_academy/screens/student_details/model/user_in_activity.dart';
import 'package:copy_with_extension/copy_with_extension.dart';
import 'package:empty_annotation/empty_annotation.dart';

part 'student_details_state.g.dart';

@Empty()
@CopyWith()
class StudentDetailsState {
  final UserInActivity student;
  final String? error;
  final bool loading;

  StudentDetailsState({
    required this.student,
    required this.error,
    required this.loading,
  });
}
