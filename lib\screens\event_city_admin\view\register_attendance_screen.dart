import 'package:city_academy/core/util/enums.dart';
import 'package:city_academy/core/util/extentions.dart';
import 'package:city_academy/core/util/global_instances.dart';
import 'package:city_academy/core/util/image_path.dart';
import 'package:city_academy/core/widgets/appbar_custom.dart';
import 'package:city_academy/core/widgets/buttons/custom_button.dart';
import 'package:city_academy/core/widgets/calendar_detail.dart';
import 'package:city_academy/core/widgets/custom_dialog.dart';
import 'package:city_academy/routes/app_routes.dart';
import 'package:city_academy/routes/routes.dart';
import 'package:city_academy/screens/event_city_admin/controller/bloc/event_city_admin_state.dart';
import 'package:city_academy/screens/event_city_admin/view/widgets/list_users.dart';
import 'package:city_academy/theme/colors.dart';
import 'package:city_academy/theme/texts.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class RegisterAttendanceScreen extends StatefulWidget {
  const RegisterAttendanceScreen({super.key});

  @override
  State<RegisterAttendanceScreen> createState() =>
      _RegisterAttendanceScreenState();
}

class _RegisterAttendanceScreenState extends State<RegisterAttendanceScreen> {
  @override
  void initState() {
    super.initState();
    eventCityAdminCubit.fetchUsers();
  }

  @override
  Widget build(BuildContext context) {
    return BlocConsumer(
      bloc: eventCityAdminCubit,
      listenWhen:
          (EventCityAdminState oldState, EventCityAdminState newState) =>
              oldState.status != newState.status,
      listener: (context, EventCityAdminState state) {
        if (state.status == EventCityAdminStatus.failed) {
          showDialog(
            context: context,
            builder: (BuildContext context) {
              return CustomDialog(
                title: 'Falha na confirmação',
                onPressed: router.pop,
                child: Text(
                  'Aconteceu algum problema ao fazer a confirmação de presença. Por favor tente novamente mais tarde.',
                  style: titleText14.copyWith(
                    color: CustomColors.neutralDark700,
                  ),
                ),
              );
            },
          );
        }
        if (state.status == EventCityAdminStatus.succeeds) {
          showDialog(
            context: context,
            builder: (BuildContext context) {
              return CustomDialog(
                title: 'Confirmação de presença realizada!',
                child: Text(
                  'Muito obrigado por realizar a confirmação de presença dos seus alunos, e boa aula',
                  style: titleText14.copyWith(
                    color: CustomColors.neutralDark700,
                    fontWeight: FontWeight.w400,
                  ),
                ),
                onPressed: () {
                  router.pop();
                  router.pop();
                  router.pop();
                },
              );
            },
          );
        }
      },
      builder: (context, EventCityAdminState state) {
        final isActivity = state.eventCity.type ==
            ActivityType.activity.toStringRepresentation();
        return Scaffold(
          backgroundColor: Colors.white,
          appBar: const AppbarCustom(title: 'Registrar presença'),
          body: SingleChildScrollView(
            child: Column(
              children: [
                CalendarDetails(eventCity: state.eventCity),
                const Divider(),
                ListUserCheckIn(
                  usersCheckIn: state.users,
                  listSelected: state.listSelected,
                  showTrailing: isActivity,
                ),
              ],
            ),
          ),
          bottomNavigationBar: isActivity
              ? Container(
                  height: 90,
                  decoration: const BoxDecoration(
                    boxShadow: [
                      BoxShadow(
                        color: Colors.grey,
                        offset: Offset(0, -2),
                        blurRadius: 2,
                      ),
                    ],
                  ),
                  child: Container(
                    color: Colors.white,
                    padding:
                        const EdgeInsets.symmetric(horizontal: 30, vertical: 8),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceAround,
                      children: [
                        Expanded(
                          child: SizedBox(
                            height: double.infinity,
                            child: CustomButton(
                              leadingIcon: ImagePath.addUser,
                              onPressed: () => router.push(
                                Routes.studentSearch,
                                extra: [true, state.eventCity],
                              ),
                              loading: state.loading,
                            ),
                          ),
                        ),
                        const SizedBox(width: 20),
                        Expanded(
                          flex: 2,
                          child: SizedBox(
                            height: double.infinity,
                            child: CustomButton(
                              title: 'Compareceu',
                              onPressed: () =>
                                  eventCityAdminCubit.action(state.eventCity),
                              loading: state.loading,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                )
              : null,
        );
      },
    );
  }
}
