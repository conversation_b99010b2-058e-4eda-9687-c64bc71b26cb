import 'package:city_academy/core/util/app_texts.dart';
import 'package:city_academy/core/util/enums.dart';
import 'package:city_academy/core/util/extentions.dart';
import 'package:city_academy/core/util/global_instances.dart';
import 'package:city_academy/core/widgets/appbar_custom.dart';
import 'package:city_academy/core/widgets/buttons/custom_button.dart';
import 'package:city_academy/core/widgets/profile_tile.dart';
import 'package:city_academy/routes/app_routes.dart';
import 'package:city_academy/routes/routes.dart';
import 'package:city_academy/screens/history/controller/cubit/history_state.dart';
import 'package:city_academy/screens/history/view/widgets/history_button.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

enum HistoryTypes {
  avaliation('Histórico Avaliações', AppTexts.assessmentsFormId),
  monitoring('Histórico Monitoramento', AppTexts.monitoringFormId);

  final String label;
  final String formId;

  const HistoryTypes(this.label, this.formId);
}

class HistoryScreen extends StatefulWidget {
  final HistoryTypes historyType;

  const HistoryScreen.assessments({super.key})
      : historyType = HistoryTypes.avaliation;

  const HistoryScreen.monitorment({super.key})
      : historyType = HistoryTypes.monitoring;

  @override
  State<HistoryScreen> createState() => _HistoryScreenState();
}

class _HistoryScreenState extends State<HistoryScreen> {
  @override
  void initState() {
    super.initState();
    historyCubit.fetchHistory(widget.historyType.formId);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppbarCustom(title: widget.historyType.label),
      body: BlocBuilder(
        bloc: historyCubit,
        builder: (context, HistoryState state) {
          return SingleChildScrollView(
            child: Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const SizedBox(height: 26),
                  ProfileTile(user: state.student),
                  const SizedBox(height: 26),
                  ...state.historyList.map((formData) {
                    return HistoryButton(
                      onTap: () {
                        formCubit.setResponseId(formData.responseId);
                        router.push(
                          widget.historyType == HistoryTypes.avaliation
                              ? Routes.assessmentsInfo
                              : Routes.monitoring,
                          extra: formData.toJson()..addAll({'readOnly': true}),
                        );
                      },
                      title: formData.title,
                      subTitle: formData.createdAt?.formatExibition(),
                      isEdit: formData.status == FormStatus.draft,
                    );
                  }),
                  Padding(
                    padding: const EdgeInsets.symmetric(
                        horizontal: 10.0, vertical: 32),
                    child: CustomButton(
                      loading: state.loading,
                      onPressed: () {
                        if (state.historyList.isNotEmpty &&
                            state.historyList.last.status == FormStatus.draft) {
                          formCubit
                              .setResponseId(state.historyList.last.responseId);
                        }
                        return router.push(
                            widget.historyType == HistoryTypes.avaliation
                                ? Routes.assessmentsInfo
                                : Routes.monitoring);
                      },
                      title: state.historyList.isEmpty
                          ? 'Primeira Avaliação'
                          : state.historyList.last.status == FormStatus.draft
                              ? 'Continuar Avaliação'
                              : 'Adicionar Avaliação',
                    ),
                  ),
                ],
              ),
            ),
          );
        },
      ),
    );
  }
}
