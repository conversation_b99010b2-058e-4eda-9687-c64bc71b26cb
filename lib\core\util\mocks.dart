import 'dart:math';

import 'package:city_academy/core/util/enums.dart';
import 'package:city_academy/screens/activity_list/model/activity_model.dart';
import 'package:city_academy/screens/login/model/user.dart';
import 'package:city_academy/screens/register_info/model/personal_data_model.dart';
import 'package:city_academy/screens/student_details/model/user_in_activity.dart';

T getRandomItem<T>(List<T> list) => list[Random().nextInt(list.length)];

String getRandomImage() {
  final random = Random();
  final imageId = random.nextInt(100);
  return 'https://picsum.photos/250?image=$imageId';
}

List<ActivityModel> mockActivities = [
  ActivityModel(
    activityId: '1',
    activityCenterId: '101',
    startTime: DateTime.now().add(const Duration(hours: 2)),
    endTime: DateTime.now().add(const Duration(hours: 3)),
    description: "Sessão relaxante de yoga.",
    eventName: 'Yoga Relaxante',
  ),
  ActivityModel(
    activityId: '2',
    activityCenterId: '102',
    startTime: DateTime.now().add(const Duration(hours: 4)),
    endTime: DateTime.now().add(const Duration(hours: 4, minutes: 45)),
    description: "Treino intenso de Crossfit.",
    eventName: 'Crossfit Intenso',
  ),
  ActivityModel(
    activityId: '3',
    activityCenterId: '103',
    startTime: DateTime.now().add(const Duration(hours: 6)),
    endTime: DateTime.now().add(const Duration(hours: 7)),
    description: "Aula de Pilates para fortalecimento do core.",
    eventName: 'Pilates Core',
  ),
];

const personalDataModelMock = PersonalDataModel(
  email: '<EMAIL>',
  birthDate: '01/01/1990',
  emergencyContact: 'Esposa: (81) 99999-9999',
  phone: '(81) 00000-0000',
  race: 'Branco',
  sexualOrientation: 'Outro',
  genderIdentity: 'Outro',
  address: 'Outro',
);
final List<UserInActivity> mockUsersActivitie = [
  UserInActivity(
    id: "102345",
    sub: "sub1",
    type: "student",
    active: true,
    createdAt: DateTime.now(),
    updatedAt: DateTime.now(),
    profileImageId: "",
    firstName: "Maria",
    lastName: "Silva",
    nickname: "Mari",
    email: "<EMAIL>",
    phone: "11999999999",
    gender: UserGender.female,
    genderIdentity: "Feminino",
    race: "Branca",
    sexualOrientation: "Hetero",
    birthDate: DateTime(1995, 5, 20),
    address: {},
    contacts: [],
    hasConfirmation: false,
  ),
  UserInActivity(
    id: "102346",
    sub: "sub2",
    type: "student",
    active: true,
    createdAt: DateTime.now(),
    updatedAt: DateTime.now(),
    profileImageId: "",
    firstName: "José",
    lastName: "Santos",
    nickname: "Ze",
    email: "<EMAIL>",
    phone: "11988888888",
    gender: UserGender.male,
    genderIdentity: "Masculino",
    race: "Parda",
    sexualOrientation: "Hetero",
    birthDate: DateTime(1990, 3, 10),
    address: {},
    contacts: [],
    hasConfirmation: false,
  ),
  UserInActivity(
    id: "102347",
    sub: "sub3",
    type: "student",
    active: true,
    createdAt: DateTime.now(),
    updatedAt: DateTime.now(),
    profileImageId: "",
    firstName: "João",
    lastName: "Oliveira",
    nickname: "Jão",
    email: "<EMAIL>",
    phone: "11977777777",
    gender: UserGender.male,
    genderIdentity: "Masculino",
    race: "Negra",
    sexualOrientation: "Hetero",
    birthDate: DateTime(1992, 7, 15),
    address: {},
    contacts: [],
    hasConfirmation: false,
  ),
];

List<User> mockUsers = [
  User(
    id: '1',
    sub: 'sub1',
    type: 'admin',
    active: true,
    createdAt: DateTime.now().subtract(const Duration(days: 10)),
    updatedAt: DateTime.now(),
    profileImageId: 'img1',
    firstName: 'John',
    lastName: 'Doe',
    nickname: 'johnny',
    email: '<EMAIL>',
    phone: '************',
    gender: UserGender.male,
    genderIdentity: 'cisgender',
    race: 'white',
    sexualOrientation: 'heterosexual',
    birthDate: DateTime(1990, 5, 15),
    address: {'street': '123 Main St', 'city': 'Sample City'},
    contacts: ['contact1', 'contact2'],
    activityCenterId: '103',
  ),
  User(
    id: '2',
    sub: 'sub2',
    type: 'user',
    active: false,
    createdAt: DateTime.now().subtract(const Duration(days: 20)),
    updatedAt: DateTime.now(),
    profileImageId: 'img2',
    firstName: 'Jane',
    lastName: 'Smith',
    nickname: 'janie',
    email: '<EMAIL>',
    phone: '************',
    gender: UserGender.female,
    genderIdentity: 'cisgender',
    race: 'black',
    sexualOrientation: 'bisexual',
    birthDate: DateTime(1985, 7, 25),
    address: {'street': '456 Oak St', 'city': 'Example City'},
    contacts: ['contact3', 'contact4'],
    activityCenterId: '103',
  ),
  User(
    id: '3',
    sub: 'sub3',
    type: 'guest',
    active: true,
    createdAt: DateTime.now().subtract(const Duration(days: 5)),
    updatedAt: DateTime.now(),
    profileImageId: 'img3',
    firstName: 'Alex',
    lastName: 'Taylor',
    nickname: 'alexander',
    email: '<EMAIL>',
    phone: '************',
    gender: UserGender.female,
    genderIdentity: 'non-binary',
    race: 'asian',
    sexualOrientation: 'pansexual',
    birthDate: DateTime(2000, 1, 1),
    address: {'street': '789 Pine St', 'city': 'Test City'},
    contacts: ['contact5'],
    activityCenterId: '103',
  ),
];
