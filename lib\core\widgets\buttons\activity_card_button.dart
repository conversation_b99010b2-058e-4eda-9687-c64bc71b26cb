import 'package:city_academy/core/util/enums.dart';
import 'package:city_academy/core/util/extentions.dart';
import 'package:city_academy/core/util/global_instances.dart';
import 'package:city_academy/routes/app_routes.dart';
import 'package:city_academy/routes/routes.dart';
import 'package:city_academy/screens/activity_list/model/activity_model.dart';
import 'package:city_academy/theme/colors.dart';
import 'package:city_academy/theme/texts.dart';
import 'package:flutter/material.dart';

class ActivityCardButton extends StatefulWidget {
  final ActivityModel activity;
  final ActivityType activityType;

  const ActivityCardButton({
    super.key,
    required this.activity,
    required this.activityType,
  });

  @override
  State<ActivityCardButton> createState() => _ActivityCardButtonState();
}

class _ActivityCardButtonState extends State<ActivityCardButton> {
  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: () async {
        switch (widget.activityType) {
          case ActivityType.avaliation:
            await presenceFormCubit.fetchActivity(widget.activity.activityId);
            eventCityAdminCubit.setEventCity(presenceFormCubit.state.eventCity);
            router.push(Routes.registerAttendance);
            break;
          case ActivityType.meet:
            meetPresenceCubit.fetchActivity(widget.activity.activityId);
            router.push(Routes.meetPresence);
            break;
          case ActivityType.activity:
            presenceFormCubit.fetchActivity(widget.activity.activityId);
            router.push(Routes.presenceForm);
            break;
        }
      },
      child: Container(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(8),
          border: Border.all(
            width: 1,
            color: CustomColors.neutralGray500,
          ),
        ),
        child: Row(
          children: [
            Row(
              children: [
                const SizedBox(width: 16),
                Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Text(
                      '${widget.activity.startTime.getCustomHour()}h',
                      style: titleText18.copyWith(
                        fontWeight: FontWeight.w500,
                        color: CustomColors.neutralDark700,
                      ),
                    ),
                    Text(
                      widget.activity.startTime.getCustomDate(),
                      style: titleText12.copyWith(
                        fontWeight: FontWeight.w500,
                        color: CustomColors.neutralGray500,
                      ),
                    )
                  ],
                ),
                Container(
                  height: 50,
                  margin: const EdgeInsets.symmetric(horizontal: 8),
                  child: const VerticalDivider(
                    thickness: 1,
                    color: CustomColors.neutralGray200,
                    endIndent: 0,
                    indent: 0,
                  ),
                ),
              ],
            ),
            Expanded(
              flex: 2,
              child: Text(
                widget.activity.eventName,
                style: titleText16.copyWith(
                  color: CustomColors.neutralDark700,
                ),
                overflow: TextOverflow.ellipsis,
                maxLines: 2,
              ),
            ),
            const SizedBox(width: 16),
            const Icon(
              Icons.arrow_forward_ios,
              color: CustomColors.neutralDark700,
              size: 18,
            ),
            const SizedBox(width: 16),
          ],
        ),
      ),
    );
  }
}
