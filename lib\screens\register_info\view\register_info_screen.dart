import 'package:city_academy/core/util/app_texts.dart';
import 'package:city_academy/core/util/global_instances.dart';
import 'package:city_academy/core/widgets/appbar_custom.dart';
import 'package:city_academy/core/widgets/buttons/custom_button.dart';
import 'package:city_academy/core/widgets/buttons/info_menu_button.dart';
import 'package:city_academy/core/widgets/custom_divider.dart';
import 'package:city_academy/core/widgets/profile_tile.dart';
import 'package:city_academy/core/widgets/snack_bar.dart';
import 'package:city_academy/routes/app_routes.dart';
import 'package:city_academy/routes/routes.dart';
import 'package:city_academy/screens/form/controller/cubit/form_cubit.dart';
import 'package:city_academy/screens/form/controller/cubit/form_state.dart';
import 'package:city_academy/screens/form/models/form_data.dart';
import 'package:city_academy/screens/student_details/controller/bloc/student_details_bloc.dart';
import 'package:city_academy/screens/student_details/controller/bloc/student_details_state.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class RegisterInfoScreen extends StatefulWidget {
  const RegisterInfoScreen({super.key});

  @override
  State<RegisterInfoScreen> createState() => _RegisterInfoScreenState();
}

class _RegisterInfoScreenState extends State<RegisterInfoScreen> {
  @override
  void initState() {
    super.initState();
    _loadFormData();
  }

  Future<void> _loadFormData() async {
    await formCubit.fetchFormResponseList(AppTexts.registerFormId);
    if (formCubit.state.formResponses?.isNotEmpty ?? false) {
      await formCubit.setFormData(formCubit.state.formResponses!.last.toJson());
    } else {
      await formCubit.loadFormData(file: AppTexts.registerFormId);
    }
  }

  Future<void> _navigateToForm(
    BuildContext context,
    String sectionTitle,
  ) async {
    FormData? formData;
    // Use current form data if available and correct, otherwise reload
    if (formCubit.state.globalFormData != null &&
        formCubit.state.file == AppTexts.registerFormId) {
      formData = formCubit.state.globalFormData ?? formCubit.state.formData;
    } else {
      // Reload form data but preserve any existing responses
      await _loadFormData();
      formData = formCubit.state.globalFormData ?? formCubit.state.formData;
    }

    if (formData == null) {
      debugPrint('Form data is null');
      return;
    }

    final section = formData.sections.firstWhere(
      (s) => s.title == sectionTitle,
      orElse: () => throw Exception('Section not found: $sectionTitle'),
    );

    router.push(
      Routes.form,
      extra: {
        'title': section.title,
        'description': section.description,
        'sections': [section.toJson()],
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: const AppbarCustom(title: 'Informações de cadastro'),
      body: BlocBuilder<FormCubit, FormScreenState>(
        bloc: formCubit,
        builder: (context, formState) {
          if (formState.isLoading) {
            return const Center(child: CircularProgressIndicator());
          }
          if (formState.formData == null) {
            return const Center(child: Text('Nenhum dado disponível'));
          }
          return SingleChildScrollView(
            child: Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const SizedBox(height: 26),
                  BlocBuilder<StudentDetailsBloc, StudentDetailsState>(
                    bloc: studentDetailsBloc,
                    builder: (context, studentDetailsState) {
                      return ProfileTile(user: studentDetailsState.student);
                    },
                  ),
                  const SizedBox(height: 26),
                  const CustomDivider(),
                  SectionButton(
                    formData: formState.formData!,
                    sectionTitle: 'DADOS PESSOAIS',
                    buttonTitle: 'Dados pessoais',
                    onTap: _navigateToForm,
                  ),
                  SectionButton(
                    formData: formState.formData!,
                    sectionTitle: 'PAR-Q',
                    buttonTitle: 'PAR-Q',
                    onTap: _navigateToForm,
                  ),
                  SectionButton(
                    formData: formState.formData!,
                    sectionTitle: 'DCNT e outras condições de saúde',
                    buttonTitle: 'DNCT e outras condições de saúde',
                    onTap: _navigateToForm,
                  ),
                  SectionButton(
                    formData: formState.formData!,
                    sectionTitle: 'Informações do Polo',
                    buttonTitle: 'Informações do Polo',
                    onTap: _navigateToForm,
                  ),
                  SectionButton(
                    formData: formState.formData!,
                    sectionTitle: 'Informações de Saúde',
                    buttonTitle: 'Informações de Saúde',
                    onTap: _navigateToForm,
                  ),
                  SectionButton(
                    formData: formState.formData!,
                    sectionTitle: 'Informações Socioeconômicas',
                    buttonTitle: 'Informações socioeconômicas',
                    onTap: _navigateToForm,
                  ),
                  SectionButton(
                    formData: formState.formData!,
                    sectionTitle: 'TIPO DE ATENDIMENTO',
                    buttonTitle: 'Tipo de atendimento',
                    onTap: _navigateToForm,
                  ),
                  const SizedBox(height: 24),
                  CustomButton(
                    title: 'Finalizar cadastro',
                    onPressed: () async {
                      if (formCubit.validateForm()) {
                        await formCubit.saveForm(isFinishing: true);
                        if (!context.mounted) return;
                        showCustomSnackbar(
                            context, 'Formulário salvo com sucesso');
                        await studentDetailsBloc.refreshStudentData();
                        router.pop();
                      } else {
                        showErrorSnackbar(
                          context,
                          'Preencha todos os campos obrigatórios',
                        );
                      }
                    },
                  ),
                  const SizedBox(height: 24),
                ],
              ),
            ),
          );
        },
      ),
    );
  }
}
