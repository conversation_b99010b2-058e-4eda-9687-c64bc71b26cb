import 'package:city_academy/core/util/app_texts.dart';
import 'package:city_academy/core/util/global_instances.dart';
import 'package:city_academy/screens/meet/model/meet_model.dart';

class MeetRepository {
  MeetRepository();

  Future<bool> createMeet(MeetModel model) async {
    final response = await authClient.post(
      path: AppTexts.activityList(model.activityCenterId),
      body: model.toJson(),
    );

    return response.status == 201;
  }
}
