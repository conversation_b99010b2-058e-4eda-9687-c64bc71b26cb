import 'package:city_academy/core/util/app_texts.dart';
import 'package:city_academy/core/util/global_instances.dart';
import 'package:city_academy/screens/student_details/model/user_in_activity.dart';

class EventCityAdminRepository {
  EventCityAdminRepository();

  Future<bool> deleteActivity(
    String centerId,
    String activityId,
  ) async {
    final response = await authClient.delete(
      path: AppTexts.activityList(centerId) + activityId,
    );

    return response.status == 204;
  }

  Future<bool> confirmationEventInCity(
      String activityId, List<num> usersConfirmed) async {
    return true;
  }

  Future<List<UserInActivity>> fetchUsers({
    String? dayPeriod,
    List<String>? usersIds,
  }) async {
    final response = await authClient.get(
      path: AppTexts.listTrainees,
      query: {
        if (dayPeriod != null) 'day_period': dayPeriod,
        if (usersIds != null) 'ids': usersIds,
      },
    );

    return (response.data['items'] as List)
        .map((e) => UserInActivity.fromJson(e))
        .toList();
  }
}
