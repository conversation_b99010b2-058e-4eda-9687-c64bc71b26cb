import 'package:city_academy/core/api/api_client.dart';
import 'package:city_academy/core/api/api_response_service.dart';
import 'package:city_academy/core/api/auth_service.dart';
import 'package:city_academy/core/storage/storage.dart';
import 'package:city_academy/core/util/app_texts.dart';
import 'package:city_academy/screens/activity_list/controller/bloc/activity_list_cubit.dart';
import 'package:city_academy/screens/event_city_admin/controller/bloc/event_city_admin_cubit.dart';
import 'package:city_academy/screens/form/controller/cubit/form_cubit.dart';
import 'package:city_academy/screens/health_indicators/controller/cubit/health_indicators_cubit.dart';
import 'package:city_academy/screens/history/controller/cubit/history_cubit.dart';
import 'package:city_academy/screens/home/<USER>/cubit/home_cubit.dart';
import 'package:city_academy/screens/login/controller/cubit/login_cubit.dart';
import 'package:city_academy/screens/meet_presence/controller/bloc/meet_presence_cubit.dart';
import 'package:city_academy/screens/presence_form/controller/cubit/presence_form_cubit.dart';
import 'package:city_academy/screens/search_student/controller/bloc/search_students_bloc.dart';
import 'package:city_academy/screens/student_details/controller/bloc/student_details_bloc.dart';
import 'package:get_it/get_it.dart';

final getIt = GetIt.instance;

void init() {
  // Global
  getIt.registerSingleton(Storage());
  getIt.registerSingleton<ApiClient>(
    ApiClient(isAuth: true),
    instanceName: AppTexts.authClient,
  );
  getIt.registerSingleton<ApiClient>(
    ApiClient(isAuth: false),
    instanceName: AppTexts.unAuthClient,
  );

  // Services
  getIt.registerSingleton(ApiResponseService());
  getIt.registerSingleton(AuthService());

  // Cubit
  getIt.registerSingleton(ActivityListCubit());
  getIt.registerSingleton(EventCityAdminCubit());
  getIt.registerSingleton(StudentDetailsBloc());
  getIt.registerSingleton(FormCubit());
  getIt.registerSingleton(LoginCubit());
  getIt.registerSingleton(SearchStudentsBloc());
  getIt.registerSingleton(HistoryCubit());
  getIt.registerSingleton(HealthIndicatorsCubit());
  getIt.registerSingleton(HomeCubit());
  getIt.registerSingleton(PresenceFormCubit());
  getIt.registerSingleton(MeetPresenceCubit());
}
