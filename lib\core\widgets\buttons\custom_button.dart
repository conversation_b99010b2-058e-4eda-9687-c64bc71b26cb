import 'package:city_academy/theme/colors.dart';
import 'package:city_academy/theme/texts.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';

enum ButtonType { primary, secondary, error }

class CustomButton extends StatelessWidget {
  final Function()? onPressed;
  final String? title;
  final EdgeInsetsGeometry? padding;
  final ButtonType type;
  final bool? loading;
  final String? leadingIcon;
  final String? trailingIcon;
  final double? iconSize;

  const CustomButton({
    super.key,
    this.onPressed,
    this.title,
    this.padding = const EdgeInsets.symmetric(vertical: 18),
    this.type = ButtonType.primary,
    this.loading,
    this.leadingIcon,
    this.trailingIcon,
    this.iconSize = 72,
  });

  @override
  Widget build(BuildContext context) {
    return TextButton(
      onPressed: loading == true ? null : onPressed,
      style: ButtonStyle(
        backgroundColor: WidgetStateProperty.all(_getBackgroundColor()),
        padding: WidgetStateProperty.all(padding),
        shape: WidgetStateProperty.all(_getButtonShape()),
      ),
      child: loading == true
          ? const SizedBox(
              width: double.maxFinite,
              child: Center(
                child: CircularProgressIndicator(
                  color: Colors.white,
                  strokeWidth: 2,
                ),
              ),
            )
          : Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                if (leadingIcon != null)
                  SvgPicture.asset(
                    leadingIcon!,
                    width: iconSize,
                    height: iconSize,
                    fit: BoxFit.cover,
                    colorFilter: ColorFilter.mode(
                      _getTextColor(),
                      BlendMode.srcIn,
                    ),
                  ),
                if (leadingIcon != null && title != null)
                  const SizedBox(width: 6),
                if (title != null)
                  Text(
                    title!,
                    style: _getTextStyle(),
                  ),
                if (trailingIcon != null && title != null)
                  const SizedBox(width: 6),
                if (trailingIcon != null)
                  SvgPicture.asset(
                    trailingIcon!,
                    width: 20,
                    height: 20,
                    colorFilter: ColorFilter.mode(
                      _getTextColor(),
                      BlendMode.srcIn,
                    ),
                  ),
              ],
            ),
    );
  }

  Color _getBackgroundColor() {
    switch (type) {
      case ButtonType.primary:
        return CustomColors.greenDark;
      case ButtonType.error:
        return const Color(0xFFDB4437);
      default:
        return Colors.white;
    }
  }

  Color _getTextColor() {
    return type == ButtonType.secondary ? Colors.black : Colors.white;
  }

  TextStyle _getTextStyle() {
    return titleText16.copyWith(
      color: _getTextColor(),
      fontWeight: FontWeight.w500,
    );
  }

  RoundedRectangleBorder _getButtonShape() {
    return RoundedRectangleBorder(
      borderRadius: BorderRadius.circular(16),
    );
  }
}
