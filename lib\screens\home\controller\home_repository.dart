import 'package:city_academy/core/util/app_texts.dart';
import 'package:city_academy/core/util/global_instances.dart';
import 'package:city_academy/screens/home/<USER>/activity_center_model.dart';

class HomeRepository {
  Future<List<ActivityCenterModel>> fetchActivityCenters() async {
    final response = await authClient.get(
      path: AppTexts.activityCenters,
      query: {
        'page_size': '100',
      },
    );

    return (response.data['items'] as List)
        .map((e) => ActivityCenterModel.fromJson(e))
        .toList();
  }
}
