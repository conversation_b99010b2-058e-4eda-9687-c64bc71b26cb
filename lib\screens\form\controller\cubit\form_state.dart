import 'package:city_academy/screens/login/model/coach.dart';
import 'package:city_academy/screens/login/model/user.dart';
import 'package:copy_with_extension/copy_with_extension.dart';
import 'package:empty_annotation/empty_annotation.dart';

import '../../models/form_data.dart';

part 'form_state.g.dart';

@CopyWith()
@Empty()
class FormScreenState {
  final bool isLoading;
  final List<FormData>? formResponses;
  final FormData? formData;
  final FormData? globalFormData;
  final Map<String, dynamic> formValues;
  final Map<String, bool> checkboxValues;
  final Map<String, String> booleanSelections;
  final String? selectedDistrict;
  final String? file;
  final List<dynamic> healthUnitOptions;
  final bool isHealthUnitEnabled;
  final User user;
  final String? responseId;
  final List<Coach>? coachs;

  FormScreenState({
    required this.isLoading,
    required this.formValues,
    required this.checkboxValues,
    required this.booleanSelections,
    required this.isHealthUnitEnabled,
    required this.healthUnitOptions,
    required this.user,
    this.globalFormData,
    this.file,
    this.formData,
    this.selectedDistrict,
    this.formResponses,
    this.responseId,
    this.coachs,
  });
}
