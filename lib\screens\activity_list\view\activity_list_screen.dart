import 'package:city_academy/core/util/global_instances.dart';
import 'package:city_academy/core/widgets/appbar_custom.dart';
import 'package:city_academy/screens/activity_list/view/widgets/calendar_short.dart';
import 'package:city_academy/screens/activity_list/view/widgets/results_events.dart';
import 'package:flutter/material.dart';

class ActivityListScreen extends StatefulWidget {
  final String title;

  const ActivityListScreen.avaliation({
    super.key,
    this.title = 'Registrar avaliação física',
  });
  const ActivityListScreen.activities({
    super.key,
    this.title = 'Registrar presença',
  });

  @override
  State<ActivityListScreen> createState() => _ActivityListScreenState();
}

class _ActivityListScreenState extends State<ActivityListScreen> {
  DateTime selectedDate = DateTime.now();

  @override
  void initState() {
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppbarCustom(title: widget.title),
      body: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          CalendarShort(
            onChange: activityListCubit.updateSelectedDate,
          ),
          const ResultsEvents()
        ],
      ),
    );
  }
}
