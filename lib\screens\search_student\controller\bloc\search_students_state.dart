import 'package:city_academy/core/util/enums.dart';
import 'package:city_academy/screens/student_details/model/user_in_activity.dart';
import 'package:copy_with_extension/copy_with_extension.dart';
import 'package:empty_annotation/empty_annotation.dart';

part 'search_students_state.g.dart';

@Empty()
@CopyWith()
class SearchStudentsState {
  final List<UserInActivity>? students;
  final String? error;
  final bool loading;
  final SearchType type;
  final String query;

  SearchStudentsState({
    required this.students,
    required this.error,
    required this.loading,
    required this.type,
    required this.query,
  });
}
