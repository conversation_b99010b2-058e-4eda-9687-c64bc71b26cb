import 'package:city_academy/core/util/app_texts.dart';
import 'package:city_academy/core/util/global_instances.dart';
import 'package:city_academy/screens/student_details/model/user_in_activity.dart';

class StudentDetailsRepository {
  StudentDetailsRepository();

  Future<UserInActivity> getStudentDetails(String document) async {
    final response = await authClient.get(
      path: AppTexts.listTrainees,
      query: {'document': document},
    );

    return UserInActivity.fromJson(response.data);
  }
}
