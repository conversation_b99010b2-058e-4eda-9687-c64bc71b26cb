import 'package:city_academy/core/util/mocks.dart';
import 'package:city_academy/screens/student_details/model/user_in_activity.dart';

class StudentDetailsRepository {
  StudentDetailsRepository();

  Future<UserInActivity> getStudentDetails(String userId) async {
    // final response =
    //     await client.get(path: AppConstants.studentDetails + userId.toString());

    return mockUsersActivitie
        .firstWhere((user) => user.id.toString() == userId);
  }
}
