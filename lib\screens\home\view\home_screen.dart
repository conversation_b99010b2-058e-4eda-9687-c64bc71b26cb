import 'package:city_academy/core/util/enums.dart';
import 'package:city_academy/core/util/global_instances.dart';
import 'package:city_academy/core/util/image_path.dart';
import 'package:city_academy/core/widgets/appbar_custom.dart';
import 'package:city_academy/core/widgets/buttons/card_button.dart';
import 'package:city_academy/core/widgets/drawer_custom.dart';
import 'package:city_academy/routes/app_routes.dart';
import 'package:city_academy/routes/routes.dart';
import 'package:city_academy/screens/home/<USER>/cubit/home_state.dart';
import 'package:city_academy/theme/colors.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class HomeScreen extends StatefulWidget {
  const HomeScreen({super.key});

  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> {
  @override
  void initState() {
    super.initState();
    homeCubit.fetchActivityCenters();
  }

  @override
  Widget build(BuildContext context) {
    return BlocBuilder(
        bloc: homeCubit,
        builder: (context, HomeState state) {
          if (state.loading) {
            return const Center(
              child: CircularProgressIndicator(color: CustomColors.greenBase),
            );
          }

          return Scaffold(
            appBar: AppbarCustom(
                title: state.selectedActivityCenter?.activityCenterName ?? ''),
            endDrawer: const DrawerCustom(),
            body: Padding(
              padding: const EdgeInsets.symmetric(horizontal: 12),
              child: Column(
                children: [
                  const SizedBox(height: 62),
                  CardButton(
                    leadingIcon: ImagePath.addFile,
                    title: 'Registrar avaliação física',
                    subTitle: 'Para preencher o formulário da avaliação física',
                    onTap: () {
                      activityListCubit
                          .updateActivityType(ActivityType.avaliation);
                      return router.push(Routes.avaliationList);
                    },
                  ),
                  const SizedBox(height: 22),
                  CardButton(
                    leadingIcon: ImagePath.bookCheck,
                    title: 'Registro de presença',
                    subTitle:
                        'Para confirmar os usuários inscritos em suas respectivas turmas',
                    onTap: () {
                      activityListCubit
                          .updateActivityType(ActivityType.activity);
                      return router.push(Routes.activityList);
                    },
                  ),
                  const SizedBox(height: 22),
                  CardButton(
                    leadingIcon: ImagePath.calendar,
                    title: 'Reunião de equipe',
                    subTitle:
                        'Para verificar quadro de reuniões de equipe e confirmar participantes.',
                    onTap: () {
                      activityListCubit.updateActivityType(ActivityType.meet);
                      return router.push(Routes.activityList);
                    },
                  ),
                ],
              ),
            ),
          );
        });
  }
}
