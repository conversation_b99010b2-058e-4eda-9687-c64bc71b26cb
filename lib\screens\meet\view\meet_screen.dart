import 'package:city_academy/core/util/global_instances.dart';
import 'package:city_academy/core/widgets/appbar_custom.dart';
import 'package:city_academy/core/widgets/buttons/custom_button.dart';
import 'package:city_academy/core/widgets/snack_bar.dart';
import 'package:city_academy/core/widgets/textfields/text_field_custom.dart';
import 'package:city_academy/routes/app_routes.dart';
import 'package:city_academy/screens/meet/controller/meet_repository.dart';
import 'package:city_academy/screens/meet/model/meet_model.dart';
import 'package:flutter/material.dart';

class MeetScreen extends StatefulWidget {
  const MeetScreen({super.key});

  @override
  State<MeetScreen> createState() => _MeetScreenState();
}

class _MeetScreenState extends State<MeetScreen> {
  final nameController = TextEditingController();
  final descriptionController = TextEditingController();
  final dateController = TextEditingController();
  final startTimeController = TextEditingController();
  final endTimeController = TextEditingController();

  DateTime selectedDate = DateTime.now();
  late TimeOfDay startTime;
  late TimeOfDay endTime;

  @override
  void initState() {
    super.initState();
    final now = TimeOfDay.now();
    startTime = now;
    endTime = _addOneHour(now);
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _updateControllers();
    });
  }

  void _updateControllers() {
    dateController.text = _formatDate(selectedDate);
    startTimeController.text = _formatTime(startTime);
    endTimeController.text = _formatTime(endTime);
  }

  @override
  void dispose() {
    nameController.dispose();
    descriptionController.dispose();
    dateController.dispose();
    startTimeController.dispose();
    endTimeController.dispose();
    super.dispose();
  }

  TimeOfDay _addOneHour(TimeOfDay time) {
    final now = DateTime.now();
    final dateTime = DateTime(
      now.year,
      now.month,
      now.day,
      time.hour,
      time.minute,
    ).add(const Duration(hours: 1));

    return TimeOfDay(hour: dateTime.hour, minute: dateTime.minute);
  }

  Future<void> _pickDate() async {
    final picked = await showDatePicker(
      context: context,
      initialDate: selectedDate,
      firstDate: DateTime.now(),
      lastDate: DateTime(2100),
    );
    if (picked != null) {
      setState(() {
        selectedDate = picked;
        _updateControllers();
      });
    }
  }

  Future<void> _pickTime({required bool isStart}) async {
    final picked = await showTimePicker(
      context: context,
      initialTime: isStart ? startTime : endTime,
    );
    if (picked != null) {
      setState(() {
        if (isStart) {
          startTime = picked;
        } else {
          endTime = picked;
        }
        _updateControllers();
      });
    }
  }

  String _formatDate(DateTime date) {
    return '${date.day.toString().padLeft(2, '0')}/'
        '${date.month.toString().padLeft(2, '0')}/'
        '${date.year}';
  }

  String _formatTime(TimeOfDay time) {
    return time.format(context);
  }

  Widget _buildLabeledField(
      String label, VoidCallback onTap, TextEditingController controller) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        GestureDetector(
          onTap: onTap,
          child: AbsorbPointer(
            child: TextFieldCustom(
              label: label,
              controller: controller,
              onChanged: (_) {}, // obrigatório para não quebrar
            ),
          ),
        ),
      ],
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: const AppbarCustom(title: 'Criar reunião de equipe'),
      body: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 12),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const SizedBox(height: 44),
            TextFieldCustom(
              hintText: 'Nome do evento',
              controller: nameController,
            ),
            const SizedBox(height: 24),
            TextFieldCustom(
              controller: descriptionController,
              hintText:
                  'Descreva seu evento (Ex.: Nosso ponto de encontro será em frente ao Centro'
                  ' de Artesanato do Marco Zero, sugerimos o uso de tênis, roupas leves e protetor solar.)',
              maxLines: 5,
            ),
            const SizedBox(height: 24),
            _buildLabeledField(
              'Data da reunião',
              _pickDate,
              dateController,
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: _buildLabeledField(
                    'Início',
                    () => _pickTime(isStart: true),
                    startTimeController,
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: _buildLabeledField(
                    'Fim',
                    () => _pickTime(isStart: false),
                    endTimeController,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 44),
            CustomButton(
              title: 'Salvar',
              onPressed: () async {
                final name = nameController.text.trim();
                final description = descriptionController.text.trim();

                if (name.isEmpty || description.isEmpty) {
                  showErrorSnackbar(context, 'Preencha todos os campos');
                  return;
                }

                final startDateTime = DateTime(
                  selectedDate.year,
                  selectedDate.month,
                  selectedDate.day,
                  startTime.hour,
                  startTime.minute,
                );

                final endDateTime = DateTime(
                  selectedDate.year,
                  selectedDate.month,
                  selectedDate.day,
                  endTime.hour,
                  endTime.minute,
                );

                final meetModel = MeetModel(
                  name: name,
                  description: description,
                  startTime: startDateTime,
                  endTime: endDateTime,
                  activityCenterId: loginCubit.state.user.activityCenterId!,
                  coachId: loginCubit.state.user.id,
                );

                final meetRepository = MeetRepository();
                final success = await meetRepository.createMeet(meetModel);

                if (!context.mounted) return;

                if (success) {
                  showCustomSnackbar(context, 'Reunião criada com sucesso');
                  router.pop();
                } else {
                  showErrorSnackbar(context, 'Erro ao criar reunião');
                }
              },
            ),
          ],
        ),
      ),
    );
  }
}
