import 'package:city_academy/core/util/global_instances.dart';
import 'package:city_academy/core/widgets/checkbox_tile.dart';
import 'package:city_academy/screens/form/controller/cubit/form_state.dart';
import 'package:city_academy/screens/form/models/form_field_model.dart';
import 'package:flutter/material.dart';

Widget buildCheckbox(
  FormFieldModel field,
  FormScreenState state,
  bool isEnabled,
) {
  final value = state.checkboxValues[field.name] ?? false;

  return Padding(
    padding: const EdgeInsets.only(bottom: 16.0),
    child: CheckboxTile(
      title: field.label ?? '',
      value: value,
      onChanged: isEnabled
          ? null
          : (value) {
              if (value != null) {
                formCubit.updateCheckboxValue(field.name, value);
              }
            },
    ),
  );
}
