import 'package:city_academy/core/util/enums.dart';
import 'package:city_academy/screens/form/controller/cubit/form_state.dart';

String getRiskClassification(int score) {
  String riskClassification = '';
  if (score < 7) {
    riskClassification = 'baixo';
  } else if (score >= 7 && score <= 11) {
    riskClassification = 'levemente_elevado';
  } else if (score >= 12 && score <= 14) {
    riskClassification = 'moderado';
  } else if (score >= 15 && score <= 20) {
    riskClassification = 'alto';
  } else {
    riskClassification = 'muito_alto';
  }

  return riskClassification;
}

int calculateFindRiskScore(FormScreenState state) {
  int score = 0;
  final values = state.formValues;
  final booleans = state.booleanSelections;
  final user = state.user;

  // 1. Idade
  final today = DateTime.now();
  int age = today.year - user.birthDate.year;
  if (today.month < user.birthDate.month ||
      (today.month == user.birthDate.month && today.day < user.birthDate.day)) {
    age--;
  }

  if (age < 45) {
    score += 0;
  } else if (age >= 45 && age <= 54) {
    score += 2;
  } else if (age >= 55 && age <= 64) {
    score += 3;
  } else {
    score += 4;
  }

  // 2. IMC
  final bmi = double.tryParse(values['bmi']?.toString() ?? '0') ?? 0;
  if (bmi < 25) {
    score += 0;
  } else if (bmi >= 25 && bmi <= 30) {
    score += 1;
  } else {
    score += 3;
  }

  // 3. Circunferência da cintura
  final waistCircumference =
      double.tryParse(values['waist_circumference']?.toString() ?? '0') ?? 0;
  final gender = user.gender;

  if (gender == UserGender.male) {
    if (waistCircumference < 94) {
      score += 0;
    } else if (waistCircumference >= 94 && waistCircumference <= 102) {
      score += 3;
    } else {
      score += 4;
    }
  } else {
    if (waistCircumference < 80) {
      score += 0;
    } else if (waistCircumference >= 80 && waistCircumference <= 88) {
      score += 3;
    } else {
      score += 4;
    }
  }

  // 4. Atividade física diária
  final physicalActivity = booleans['physical_activity_daily'] ?? 'nao';
  if (physicalActivity == 'sim') {
    score += 0;
  } else {
    score += 2;
  }

  // 5. Frequência de consumo de alimentos saudáveis
  final healthyFood = values['healthy_food_frequency'] ?? '';
  if (healthyFood == 'todos_os_dias') {
    score += 0;
  } else {
    score += 1;
  }

  // 6. Medicamento para pressão alta
  final bloodPressureMed =
      booleans['regular_medication_for_high_blood_pressure'] ?? 'nao';
  if (bloodPressureMed == 'sim') {
    score += 2;
  }

  // 7. Histórico de glicose alta
  final highGlucose = booleans['high_blood_glucose_history'] ?? 'nao';
  if (highGlucose == 'sim') {
    score += 5;
  }

  // 8. Histórico familiar de diabetes
  final familyDiabetes = values['family_diabetes_history'] ?? '';
  if (familyDiabetes == 'sim_avos_tia_tio_primo') {
    score += 3;
  } else if (familyDiabetes == 'sim_pai_mae_irmao_irma_filho') {
    score += 5;
  }
  return score;
}
