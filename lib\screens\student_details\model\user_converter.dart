import 'package:city_academy/screens/login/model/user.dart';
import 'package:city_academy/screens/student_details/model/user_in_activity.dart';

class UserConverter {
  static UserInActivity toUserInActivity(User user) {
    return UserInActivity(
      id: user.id,
      sub: user.sub,
      type: user.type,
      active: user.active,
      createdAt: user.createdAt,
      updatedAt: user.updatedAt,
      profileImageId: user.profileImageId,
      firstName: user.firstName,
      lastName: user.lastName,
      nickname: user.nickname,
      email: user.email,
      phone: user.phone,
      gender: user.gender,
      genderIdentity: user.genderIdentity,
      race: user.race,
      sexualOrientation: user.sexualOrientation,
      birthDate: user.birthDate,
      address: user.address,
      contacts: user.contacts,
    );
  }

  static User toUser(UserInActivity userInActivity) {
    return User(
      id: userInActivity.id,
      sub: userInActivity.sub,
      type: userInActivity.type,
      active: userInActivity.active,
      createdAt: userInActivity.createdAt,
      updatedAt: userInActivity.updatedAt,
      profileImageId: userInActivity.profileImageId,
      firstName: userInActivity.firstName,
      lastName: userInActivity.lastName,
      nickname: userInActivity.nickname,
      email: userInActivity.email,
      phone: userInActivity.phone,
      gender: userInActivity.gender,
      genderIdentity: userInActivity.genderIdentity,
      race: userInActivity.race,
      sexualOrientation: userInActivity.sexualOrientation,
      birthDate: userInActivity.birthDate,
      address: userInActivity.address,
      contacts: userInActivity.contacts,
    );
  }
}
