import 'package:city_academy/core/util/global_instances.dart';
import 'package:city_academy/core/widgets/custom_checkbox.dart';
import 'package:city_academy/core/widgets/custom_divider.dart';
import 'package:city_academy/screens/login/model/coach.dart';
import 'package:city_academy/theme/colors.dart';
import 'package:city_academy/theme/texts.dart';
import 'package:flutter/material.dart';

class ListCoachCheckIn extends StatelessWidget {
  final List<Coach> usersCheckIn;
  final List<String> listSelected;

  const ListCoachCheckIn({
    super.key,
    required this.usersCheckIn,
    required this.listSelected,
  });

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'Solicitações',
                style: titleText16.copyWith(color: CustomColors.neutralDark700),
              ),
              Text(
                'Compareceram',
                style: titleText12.copyWith(color: CustomColors.neutralDark700),
              )
            ],
          ),
          const SizedBox(height: 16),
          if (usersCheckIn.isEmpty)
            SizedBox(
                height: 300,
                child: Center(
                    child: Text(
                  'Sem usuários para confirmar presença',
                  style: titleText14.copyWith(
                    fontWeight: FontWeight.w500,
                    color: CustomColors.neutralBlack,
                  ),
                )))
          else
            ...usersCheckIn.map(
              (user) {
                return Column(
                  children: [
                    const CustomDivider(),
                    ListTile(
                      leading: const CircleAvatar(
                        backgroundColor: CustomColors.neutralGray200,
                        child: Icon(
                          Icons.person,
                          color: Colors.white,
                        ),
                      ),
                      title: Text(
                        user.name,
                        style: titleText16.copyWith(
                            color: CustomColors.neutralDark700),
                      ),
                      subtitle: Text(
                        'CNS: ${user.cns ?? ''}\n${user.cref?.isNotEmpty ?? false ? 'CREF: ${user.cref}' : ''}',
                        style: titleText12.copyWith(
                          fontWeight: FontWeight.w300,
                          color: CustomColors.neutralDark700,
                        ),
                      ),
                      trailing: CustomCheckbox(
                          value: listSelected.contains(user.id),
                          onChanged: (newValue) {
                            if (newValue == true) {
                              meetPresenceCubit.setUserInConfirmation(user.id);
                            } else {
                              meetPresenceCubit
                                  .removeUserInConfirmation(user.id);
                            }
                          }),
                    ),
                  ],
                );
              },
            )
        ],
      ),
    );
  }
}
