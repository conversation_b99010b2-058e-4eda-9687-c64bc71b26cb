import 'package:city_academy/core/widgets/appbar_custom.dart';
import 'package:city_academy/theme/colors.dart';
import 'package:city_academy/theme/texts.dart';
import 'package:flutter/material.dart';

class BaseFormScreen extends StatelessWidget {
  final String title;
  final String? description;
  final List<Widget> children;

  const BaseFormScreen({
    super.key,
    required this.title,
    this.description,
    required this.children,
  });

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppbarCustom(title: title),
      body: SingleChildScrollView(
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const SizedBox(height: 12),
              if (description != null)
                Text(
                  description!,
                  style: titleText16.copyWith(
                    color: CustomColors.neutralGray600,
                  ),
                ),
              const SizedBox(height: 24),
              ...children,
            ],
          ),
        ),
      ),
    );
  }
}
