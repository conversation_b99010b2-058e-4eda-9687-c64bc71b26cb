import 'package:city_academy/core/util/global_instances.dart';
import 'package:city_academy/core/util/image_path.dart';
import 'package:city_academy/core/widgets/buttons/custom_button.dart';
import 'package:city_academy/core/widgets/snack_bar.dart';
import 'package:city_academy/core/widgets/textfields/email_text_field.dart';
import 'package:city_academy/core/widgets/textfields/password_text_field.dart';
import 'package:city_academy/routes/app_routes.dart';
import 'package:city_academy/routes/routes.dart';
import 'package:city_academy/screens/login/controller/cubit/login_state.dart';
import 'package:city_academy/theme/colors.dart';
import 'package:city_academy/theme/texts.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:url_launcher/url_launcher.dart';

class LoginScreen extends StatefulWidget {
  const LoginScreen({super.key});

  @override
  State<LoginScreen> createState() => _LoginScreenState();
}

class _LoginScreenState extends State<LoginScreen> {
  @override
  void initState() {
    super.initState();
    if (kDebugMode) {
      loginCubit.updateEmail('<EMAIL>');
      loginCubit.updatePassword('Felipe.147');
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: BlocBuilder(
        bloc: loginCubit,
        builder: (context, LoginState state) {
          return Center(
            child: SingleChildScrollView(
              child: Padding(
                padding: const EdgeInsets.symmetric(horizontal: 16),
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    Image.asset(ImagePath.logo, height: 150),
                    const SizedBox(height: 40),
                    Text(
                      'Entrar',
                      style: heading4.copyWith(
                        fontWeight: FontWeight.w500,
                        color: CustomColors.greenDark,
                      ),
                    ),
                    Padding(
                      padding: const EdgeInsets.symmetric(vertical: 24.0),
                      child: EmailTextField(
                        onChanged: loginCubit.updateEmail,
                      ),
                    ),
                    PasswordTextfield(
                      onChanged: loginCubit.updatePassword,
                    ),
                    Padding(
                      padding: const EdgeInsets.all(24.0),
                      child: CustomButton(
                        onPressed: () async {
                          await loginCubit.onLoginPressed();
                          if (loginCubit.state.user.id.isNotEmpty) {
                            router.go(Routes.home);
                            return;
                          } else {
                            if (!context.mounted) return;
                            showErrorSnackbar(
                              context,
                              state.error.isNotEmpty
                                  ? state.error
                                  : 'Credenciais inválidas. Verifique seu email e senha.',
                            );
                          }
                        },
                        title: 'Entrar',
                      ),
                    ),
                    TextButton(
                        onPressed: () async {
                          if (!await launchUrl(Uri.parse(
                              'https://www.vivendocfit.com.br/termos-seguranca-privacidade'))) {
                            throw Exception('Could not launch url');
                          }
                        },
                        child: Text('Termos de uso e privacidade',
                            style: titleText16.copyWith(
                              fontWeight: FontWeight.w500,
                              color: CustomColors.greenDark,
                            ))),
                  ],
                ),
              ),
            ),
          );
        },
      ),
    );
  }
}
