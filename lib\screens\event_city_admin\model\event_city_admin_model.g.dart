// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'event_city_admin_model.dart';

// **************************************************************************
// CopyWithGenerator
// **************************************************************************

abstract class _$EventCityAdminModelCWProxy {
  /// This function **does support** nullification of nullable fields. All `null` values passed to `non-nullable` fields will be ignored.
  ///
  /// Usage
  /// ```dart
  /// EventCityAdminModel(...).copyWith(id: 12, name: "My name")
  /// ````
  EventCityAdminModel call({
    String activityId,
    String activityCenterId,
    DateTime startTime,
    DateTime endTime,
    String description,
    String eventName,
    Coach? coach,
    String type,
    DateTime activityTime,
    bool active,
    String duration,
    int maxQtd,
    int minQtd,
    String status,
    String recurrenceId,
    int numParticipantes,
    int numAvaliacoesAlteradas,
    int atividadeTipo,
    List<int> temasParaReuniao,
    List<int> temasParaSaude,
    List<int> praticasEmSaude,
    List<int> publicoAlvo,
    int turno,
    bool pseEducacao,
    bool pseSaude,
    int inep,
    String id,
    String outraLocalidade,
    String cnesLocalAtividade,
    List<String> users,
    String coachId,
    List<String> coachs,
    DateTime createdAt,
    DateTime updatedAt,
  });
}

/// Proxy class for `copyWith` functionality. This is a callable class and can be used as follows: `instanceOfEventCityAdminModel.copyWith(...)`.
class _$EventCityAdminModelCWProxyImpl implements _$EventCityAdminModelCWProxy {
  const _$EventCityAdminModelCWProxyImpl(this._value);

  final EventCityAdminModel _value;

  @override

  /// This function **does support** nullification of nullable fields. All `null` values passed to `non-nullable` fields will be ignored.
  ///
  /// Usage
  /// ```dart
  /// EventCityAdminModel(...).copyWith(id: 12, name: "My name")
  /// ````
  EventCityAdminModel call({
    Object? activityId = const $CopyWithPlaceholder(),
    Object? activityCenterId = const $CopyWithPlaceholder(),
    Object? startTime = const $CopyWithPlaceholder(),
    Object? endTime = const $CopyWithPlaceholder(),
    Object? description = const $CopyWithPlaceholder(),
    Object? eventName = const $CopyWithPlaceholder(),
    Object? coach = const $CopyWithPlaceholder(),
    Object? type = const $CopyWithPlaceholder(),
    Object? activityTime = const $CopyWithPlaceholder(),
    Object? active = const $CopyWithPlaceholder(),
    Object? duration = const $CopyWithPlaceholder(),
    Object? maxQtd = const $CopyWithPlaceholder(),
    Object? minQtd = const $CopyWithPlaceholder(),
    Object? status = const $CopyWithPlaceholder(),
    Object? recurrenceId = const $CopyWithPlaceholder(),
    Object? numParticipantes = const $CopyWithPlaceholder(),
    Object? numAvaliacoesAlteradas = const $CopyWithPlaceholder(),
    Object? atividadeTipo = const $CopyWithPlaceholder(),
    Object? temasParaReuniao = const $CopyWithPlaceholder(),
    Object? temasParaSaude = const $CopyWithPlaceholder(),
    Object? praticasEmSaude = const $CopyWithPlaceholder(),
    Object? publicoAlvo = const $CopyWithPlaceholder(),
    Object? turno = const $CopyWithPlaceholder(),
    Object? pseEducacao = const $CopyWithPlaceholder(),
    Object? pseSaude = const $CopyWithPlaceholder(),
    Object? inep = const $CopyWithPlaceholder(),
    Object? id = const $CopyWithPlaceholder(),
    Object? outraLocalidade = const $CopyWithPlaceholder(),
    Object? cnesLocalAtividade = const $CopyWithPlaceholder(),
    Object? users = const $CopyWithPlaceholder(),
    Object? coachId = const $CopyWithPlaceholder(),
    Object? coachs = const $CopyWithPlaceholder(),
    Object? createdAt = const $CopyWithPlaceholder(),
    Object? updatedAt = const $CopyWithPlaceholder(),
  }) {
    return EventCityAdminModel(
      activityId: activityId == const $CopyWithPlaceholder()
          ? _value.activityId
          // ignore: cast_nullable_to_non_nullable
          : activityId as String,
      activityCenterId: activityCenterId == const $CopyWithPlaceholder()
          ? _value.activityCenterId
          // ignore: cast_nullable_to_non_nullable
          : activityCenterId as String,
      startTime: startTime == const $CopyWithPlaceholder()
          ? _value.startTime
          // ignore: cast_nullable_to_non_nullable
          : startTime as DateTime,
      endTime: endTime == const $CopyWithPlaceholder()
          ? _value.endTime
          // ignore: cast_nullable_to_non_nullable
          : endTime as DateTime,
      description: description == const $CopyWithPlaceholder()
          ? _value.description
          // ignore: cast_nullable_to_non_nullable
          : description as String,
      eventName: eventName == const $CopyWithPlaceholder()
          ? _value.eventName
          // ignore: cast_nullable_to_non_nullable
          : eventName as String,
      coach: coach == const $CopyWithPlaceholder()
          ? _value.coach
          // ignore: cast_nullable_to_non_nullable
          : coach as Coach?,
      type: type == const $CopyWithPlaceholder()
          ? _value.type
          // ignore: cast_nullable_to_non_nullable
          : type as String,
      activityTime: activityTime == const $CopyWithPlaceholder()
          ? _value.activityTime
          // ignore: cast_nullable_to_non_nullable
          : activityTime as DateTime,
      active: active == const $CopyWithPlaceholder()
          ? _value.active
          // ignore: cast_nullable_to_non_nullable
          : active as bool,
      duration: duration == const $CopyWithPlaceholder()
          ? _value.duration
          // ignore: cast_nullable_to_non_nullable
          : duration as String,
      maxQtd: maxQtd == const $CopyWithPlaceholder()
          ? _value.maxQtd
          // ignore: cast_nullable_to_non_nullable
          : maxQtd as int,
      minQtd: minQtd == const $CopyWithPlaceholder()
          ? _value.minQtd
          // ignore: cast_nullable_to_non_nullable
          : minQtd as int,
      status: status == const $CopyWithPlaceholder()
          ? _value.status
          // ignore: cast_nullable_to_non_nullable
          : status as String,
      recurrenceId: recurrenceId == const $CopyWithPlaceholder()
          ? _value.recurrenceId
          // ignore: cast_nullable_to_non_nullable
          : recurrenceId as String,
      numParticipantes: numParticipantes == const $CopyWithPlaceholder()
          ? _value.numParticipantes
          // ignore: cast_nullable_to_non_nullable
          : numParticipantes as int,
      numAvaliacoesAlteradas:
          numAvaliacoesAlteradas == const $CopyWithPlaceholder()
              ? _value.numAvaliacoesAlteradas
              // ignore: cast_nullable_to_non_nullable
              : numAvaliacoesAlteradas as int,
      atividadeTipo: atividadeTipo == const $CopyWithPlaceholder()
          ? _value.atividadeTipo
          // ignore: cast_nullable_to_non_nullable
          : atividadeTipo as int,
      temasParaReuniao: temasParaReuniao == const $CopyWithPlaceholder()
          ? _value.temasParaReuniao
          // ignore: cast_nullable_to_non_nullable
          : temasParaReuniao as List<int>,
      temasParaSaude: temasParaSaude == const $CopyWithPlaceholder()
          ? _value.temasParaSaude
          // ignore: cast_nullable_to_non_nullable
          : temasParaSaude as List<int>,
      praticasEmSaude: praticasEmSaude == const $CopyWithPlaceholder()
          ? _value.praticasEmSaude
          // ignore: cast_nullable_to_non_nullable
          : praticasEmSaude as List<int>,
      publicoAlvo: publicoAlvo == const $CopyWithPlaceholder()
          ? _value.publicoAlvo
          // ignore: cast_nullable_to_non_nullable
          : publicoAlvo as List<int>,
      turno: turno == const $CopyWithPlaceholder()
          ? _value.turno
          // ignore: cast_nullable_to_non_nullable
          : turno as int,
      pseEducacao: pseEducacao == const $CopyWithPlaceholder()
          ? _value.pseEducacao
          // ignore: cast_nullable_to_non_nullable
          : pseEducacao as bool,
      pseSaude: pseSaude == const $CopyWithPlaceholder()
          ? _value.pseSaude
          // ignore: cast_nullable_to_non_nullable
          : pseSaude as bool,
      inep: inep == const $CopyWithPlaceholder()
          ? _value.inep
          // ignore: cast_nullable_to_non_nullable
          : inep as int,
      id: id == const $CopyWithPlaceholder()
          ? _value.id
          // ignore: cast_nullable_to_non_nullable
          : id as String,
      outraLocalidade: outraLocalidade == const $CopyWithPlaceholder()
          ? _value.outraLocalidade
          // ignore: cast_nullable_to_non_nullable
          : outraLocalidade as String,
      cnesLocalAtividade: cnesLocalAtividade == const $CopyWithPlaceholder()
          ? _value.cnesLocalAtividade
          // ignore: cast_nullable_to_non_nullable
          : cnesLocalAtividade as String,
      users: users == const $CopyWithPlaceholder()
          ? _value.users
          // ignore: cast_nullable_to_non_nullable
          : users as List<String>,
      coachId: coachId == const $CopyWithPlaceholder()
          ? _value.coachId
          // ignore: cast_nullable_to_non_nullable
          : coachId as String,
      coachs: coachs == const $CopyWithPlaceholder()
          ? _value.coachs
          // ignore: cast_nullable_to_non_nullable
          : coachs as List<String>,
      createdAt: createdAt == const $CopyWithPlaceholder()
          ? _value.createdAt
          // ignore: cast_nullable_to_non_nullable
          : createdAt as DateTime,
      updatedAt: updatedAt == const $CopyWithPlaceholder()
          ? _value.updatedAt
          // ignore: cast_nullable_to_non_nullable
          : updatedAt as DateTime,
    );
  }
}

extension $EventCityAdminModelCopyWith on EventCityAdminModel {
  /// Returns a callable class that can be used as follows: `instanceOfEventCityAdminModel.copyWith(...)`.
  // ignore: library_private_types_in_public_api
  _$EventCityAdminModelCWProxy get copyWith =>
      _$EventCityAdminModelCWProxyImpl(this);

  /// Copies the object with the specific fields set to `null`. If you pass `false` as a parameter, nothing will be done and it will be ignored. Don't do it. Prefer `copyWith(field: null)`.
  ///
  /// Usage
  /// ```dart
  /// EventCityAdminModel(...).copyWithNull(firstField: true, secondField: true)
  /// ````
  EventCityAdminModel copyWithNull({
    bool coach = false,
  }) {
    return EventCityAdminModel(
      activityId: activityId,
      activityCenterId: activityCenterId,
      startTime: startTime,
      endTime: endTime,
      description: description,
      eventName: eventName,
      coach: coach == true ? null : this.coach,
      type: type,
      activityTime: activityTime,
      active: active,
      duration: duration,
      maxQtd: maxQtd,
      minQtd: minQtd,
      status: status,
      recurrenceId: recurrenceId,
      numParticipantes: numParticipantes,
      numAvaliacoesAlteradas: numAvaliacoesAlteradas,
      atividadeTipo: atividadeTipo,
      temasParaReuniao: temasParaReuniao,
      temasParaSaude: temasParaSaude,
      praticasEmSaude: praticasEmSaude,
      publicoAlvo: publicoAlvo,
      turno: turno,
      pseEducacao: pseEducacao,
      pseSaude: pseSaude,
      inep: inep,
      id: id,
      outraLocalidade: outraLocalidade,
      cnesLocalAtividade: cnesLocalAtividade,
      users: users,
      coachId: coachId,
      coachs: coachs,
      createdAt: createdAt,
      updatedAt: updatedAt,
    );
  }
}

// **************************************************************************
// EmptyGenerator
// **************************************************************************

class EventCityAdminModelEmpty extends EventCityAdminModel {
  EventCityAdminModelEmpty()
      : super(
            coach: null,
            type: '',
            activityTime: DateTime.now(),
            active: false,
            duration: '',
            maxQtd: 0,
            minQtd: 0,
            status: '',
            recurrenceId: '',
            numParticipantes: 0,
            numAvaliacoesAlteradas: 0,
            atividadeTipo: 0,
            temasParaReuniao: const [],
            temasParaSaude: const [],
            praticasEmSaude: const [],
            publicoAlvo: const [],
            turno: 0,
            pseEducacao: false,
            pseSaude: false,
            inep: 0,
            id: '',
            outraLocalidade: '',
            cnesLocalAtividade: '',
            users: const [],
            coachId: '',
            coachs: const [],
            createdAt: DateTime.now(),
            updatedAt: DateTime.now(),
            activityId: '',
            activityCenterId: '',
            startTime: DateTime.now(),
            endTime: DateTime.now(),
            description: '',
            eventName: '');
}
