const monthByInt = {
  1: 'Janeiro',
  2: '<PERSON><PERSON>',
  3: '<PERSON><PERSON><PERSON>',
  4: '<PERSON><PERSON><PERSON>',
  5: '<PERSON><PERSON>',
  6: '<PERSON><PERSON>',
  7: '<PERSON><PERSON>',
  8: 'Agosto',
  9: '<PERSON><PERSON><PERSON>',
  10: '<PERSON><PERSON><PERSON>',
  11: '<PERSON><PERSON><PERSON>',
  12: '<PERSON><PERSON><PERSON><PERSON>',
};

const dayByInt = {
  1: 'Segunda',
  2: '<PERSON><PERSON><PERSON>',
  3: '<PERSON>ua<PERSON>',
  4: '<PERSON>uin<PERSON>',
  5: '<PERSON><PERSON>',
  6: 'Sábad<PERSON>',
  7: '<PERSON>',
};

String getMonthByInt(
  int code, {
  bool abbreviation = false,
}) =>
    abbreviation ? monthByInt[code]!.abbreviation() : monthByInt[code]!;

String getDayByInt(
  int code, {
  bool abbreviation = false,
}) =>
    abbreviation ? dayByInt[code]!.abbreviation() : dayByInt[code]!;

List<DateTime> buildArrayDate(
  DateTime initialDate, [
  int count = 7,
]) {
  final referenceDate = DateTime(
    initialDate.year,
    initialDate.month,
    initialDate.day,
  );
  final arrayDates = <DateTime>[];
  for (int iterator = 0; iterator < count; iterator++) {
    if (iterator == 0) {
      arrayDates.add(initialDate);
    } else {
      arrayDates.add(referenceDate.add(Duration(days: iterator)));
    }
  }
  return arrayDates;
}

extension on String {
  String abbreviation() {
    return substring(0, 3);
  }
}
