import 'package:city_academy/core/util/global_instances.dart';
import 'package:city_academy/core/widgets/checkbox_tile.dart';
import 'package:city_academy/screens/form/controller/cubit/form_state.dart';
import 'package:city_academy/screens/form/models/form_field_model.dart';
import 'package:city_academy/theme/colors.dart';
import 'package:flutter/material.dart';

Widget buildBoolean(
  FormFieldModel field,
  FormScreenState state,
  bool isEnabled,
) {
  return Padding(
    padding: const EdgeInsets.only(bottom: 16.0),
    child: Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          field.label ?? '',
          style: const TextStyle(
            color: CustomColors.neutralDark900,
            fontWeight: FontWeight.w500,
          ),
        ),
        const SizedBox(height: 8.0),
        CheckboxTile(
          title: "Sim",
          value: state.booleanSelections[field.name] == 'sim',
          onChanged: isEnabled
              ? null
              : (value) {
                  if (value == true) {
                    formCubit.updateBooleanSelection(field.name, 'sim');
                  }
                },
        ),
        const SizedBox(height: 12.0),
        CheckboxTile(
          title: "Não",
          value: state.booleanSelections[field.name] == 'nao',
          onChanged: isEnabled
              ? null
              : (value) {
                  if (value == true) {
                    formCubit.updateBooleanSelection(field.name, 'nao');
                  }
                },
        ),
      ],
    ),
  );
}
