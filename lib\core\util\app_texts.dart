class AppTexts {
  // Global
  static const String unAuthClient = 'UnauthenticatedClient';
  static const String authClient = 'AuthenticatedClient';
  static const String baseUrl = 'pacapidev.vivendocfit.com';
  static const String registerFormId = 'b536666a-2026-4d5e-9c73-77ca4092b109';
  static const String assessmentsFormId =
      'd0b89a36-6214-4684-94c5-f257ee832a48';
  static const String monitoringFormId = '6a346834-601b-4454-997f-d6aa96ad36af';

  // API
  static const String activities = '/Configuracao/Prazos';
  static const String activityCenters = '/activity-centers/';
  static const String login = '/users/coach/login';
  static const String coach = '/users/coaches';
  static const String getCoachInfo = '/users/coach/me';
  static const String refreshToken = '/users/coach/refreshtoken';
  static const String listTrainees = '/users/trainees';
  static String getUser(String userId) => '/users/$userId';
  static const String forms = '/forms/';
  static String formsResponse(String formId) => '/forms/$formId/responses/';
  static String activityList(String value) =>
      '/activity-centers/$value/activities/';

  // Shared Key
  static const String idTokenKey = 'cfit_token';
  static const String refreshTokenKey = 'cfit_refreshToken';
  static const String localId = 'localId';
}
