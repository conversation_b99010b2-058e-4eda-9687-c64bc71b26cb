import 'dart:convert';

import 'package:city_academy/core/api/api_response_service.dart';
import 'package:city_academy/core/api/exceptions.dart';
import 'package:city_academy/core/util/app_texts.dart';
import 'package:city_academy/core/util/global_instances.dart';
import 'package:city_academy/injection.dart';
import 'package:flutter/foundation.dart';
import 'package:http/http.dart' as http;
import 'package:http_parser/http_parser.dart';

class ApiClient {
  final String baseUri = AppTexts.baseUrl;
  final bool isAuth;

  ApiClient({required this.isAuth});

  Future<ApiResponse> _request({
    required String method,
    required String path,
    Map<String, dynamic>? query,
    dynamic body,
    FileData? fileData,
  }) async {
    late final http.Response response;
    final uri = Uri.https(baseUri, path, query);
    final headers = {
      'accept': 'application/json; charset=utf-8',
      'Content-Type': 'application/json; charset=utf-8',
    };

    if (isAuth) {
      String? token = await storage.get(AppTexts.idTokenKey);
      if (token != null) {
        headers['Authorization'] = 'Bearer $token';
      } else {
        throw NotLoggedUser();
      }
    }

    switch (method) {
      case 'GET':
        response = await http.get(uri, headers: headers);
        break;
      case 'POST':
        if (fileData != null) {
          var request = http.MultipartRequest('POST', uri);
          request.headers.addAll(headers);

          request.files.add(http.MultipartFile.fromBytes(
            fileData.fileKey,
            fileData.fileBytes,
            filename: fileData.fileName,
            contentType: fileData.contentType,
          ));

          if (body != null) {
            body.forEach((key, value) {
              request.fields[key] = value.toString();
            });
          }

          final streamedResponse = await request.send();
          response = await http.Response.fromStream(streamedResponse);
          break;
        }

        response =
            await http.post(uri, body: jsonEncode(body), headers: headers);
        break;
      case 'DELETE':
        response = await http.delete(uri, headers: headers);
        break;
      case 'PATCH':
        response =
            await http.patch(uri, body: jsonEncode(body), headers: headers);
        break;
      case 'PUT':
        response =
            await http.put(uri, body: jsonEncode(body), headers: headers);
        break;
      default:
        throw UnsupportedError('Unsupported HTTP method: $method');
    }

    return getIt<ApiResponseService>().fromHttpResponse(response);
  }

  Future<ApiResponse> performRequestWithRetry({
    required String method,
    required String path,
    int retries = 2,
    Map<String, dynamic>? query,
    dynamic body,
    FileData? fileData,
  }) async {
    try {
      return await _request(
        method: method,
        path: path,
        query: query,
        body: body,
        fileData: fileData,
      );
    } on UnauthorizedException catch (e) {
      debugPrint('UnauthorizedException capturada: ${e.message}');
      if (retries == 0) {
        debugPrint('Sem mais tentativas, rethrow da exceção');
        rethrow;
      }

      // Tenta renovar o token apenas se não estivermos verificando autenticação
      if (path != AppTexts.getCoachInfo) {
        debugPrint('Tentando renovar token para endpoint: $path');
        final refreshed = await authService.refreshToken();
        if (!refreshed) {
          debugPrint('Falha ao renovar token, rethrow da exceção');
          rethrow;
        }
        debugPrint('Token renovado com sucesso, tentando novamente');
      } else {
        debugPrint(
            'Endpoint é verificação de autenticação, não tentando renovar token');
        // Para verificação de autenticação, apenas rethrow a exceção
        rethrow;
      }

      return await performRequestWithRetry(
        method: method,
        path: path,
        retries: retries - 1,
        query: query,
        body: body,
        fileData: fileData,
      );
    } on NotFoundException catch (_) {
      rethrow;
    } on ForbiddenException catch (_) {
      rethrow;
    } catch (e) {
      if (retries == 0) {
        rethrow;
      }
      return await performRequestWithRetry(
        method: method,
        path: path,
        retries: retries - 1,
        query: query,
        body: body,
        fileData: fileData,
      );
    }
  }

  Future<ApiResponse> get({
    required String path,
    int retries = 2,
    Map<String, dynamic>? query,
    bool useAuthToken = true,
  }) async {
    return performRequestWithRetry(
      method: 'GET',
      path: path,
      retries: retries,
      query: query,
    );
  }

  Future<ApiResponse> put({
    required String path,
    Map<String, dynamic>? body,
    int retries = 2,
  }) async {
    return performRequestWithRetry(
      method: 'PUT',
      path: path,
      body: body,
      retries: retries,
    );
  }

  Future<ApiResponse> post({
    required String path,
    dynamic body,
    int retries = 2,
    bool useAuthToken = true,
    FileData? fileData,
  }) async {
    return performRequestWithRetry(
      method: 'POST',
      path: path,
      body: body,
      fileData: fileData,
      retries: retries,
    );
  }

  Future<ApiResponse> patch({
    required String path,
    dynamic body,
    int retries = 2,
    bool useAuthToken = true,
    FileData? fileData,
  }) async {
    return performRequestWithRetry(
      method: 'PATCH',
      path: path,
      body: body,
      fileData: fileData,
      retries: retries,
    );
  }

  Future<ApiResponse> delete({
    required String path,
    int retries = 3,
    Map<String, dynamic>? query,
  }) async {
    return performRequestWithRetry(
      method: 'DELETE',
      path: path,
      retries: retries,
      query: query,
    );
  }
}

class FileData {
  final MediaType contentType;
  final Uint8List fileBytes;
  final String fileName;
  final String fileKey;

  FileData({
    required this.contentType,
    required this.fileBytes,
    required this.fileName,
    this.fileKey = 'arquivo',
  });
}

class ApiResponse {
  final Map<String, dynamic> data;
  final int status;

  ApiResponse({
    required this.data,
    required this.status,
  });
}
