import 'package:city_academy/core/util/enums.dart';
import 'package:city_academy/screens/activity_list/model/activity_model.dart';
import 'package:copy_with_extension/copy_with_extension.dart';
import 'package:empty_annotation/empty_annotation.dart';

part 'activity_list_state.g.dart';

@Empty()
@CopyWith()
class ActivityListState {
  final bool isLoading;
  final List<ActivityModel> activityList;
  final String centerId;
  final DateTime? selectedDate;
  final ActivityType activityType;

  ActivityListState({
    this.selectedDate,
    required this.centerId,
    required this.isLoading,
    required this.activityList,
    required this.activityType,
  });
}
