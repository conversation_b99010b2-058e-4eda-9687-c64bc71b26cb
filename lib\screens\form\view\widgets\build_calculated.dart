import 'package:city_academy/core/widgets/textfields/text_field_custom.dart';
import 'package:city_academy/screens/form/controller/cubit/form_state.dart';
import 'package:city_academy/screens/form/models/form_field_model.dart';
import 'package:flutter/material.dart';
import 'package:math_expressions/math_expressions.dart';

Widget buildCalculated(FormFieldModel field, FormScreenState state) {
  final String initialValue = (state.formValues[field.name] ?? '').toString();

  return Padding(
    padding: const EdgeInsets.only(bottom: 16.0),
    child: TextFieldCustom(
      isEnabled: false,
      label: field.label,
      hintText: initialValue,
    ),
  );
}

String evaluateFormula(String formula, Map<String, dynamic> formValues) {
  String parsedFormula = formula;

  // Substitui todos os campos do tipo {{field}}
  final RegExp regExp = RegExp(r'\{\{(\w+)\}\}');
  parsedFormula = parsedFormula.replaceAllMapped(regExp, (match) {
    final key = match.group(1)!;
    final value = formValues[key];
    return value != null ? value.toString() : '0';
  });

  try {
    final result = _calculateExpression(parsedFormula);
    return result.toStringAsFixed(2);
  } catch (e) {
    return '';
  }
}

double _calculateExpression(String expression) {
  final p = GrammarParser();
  Expression exp = p.parse(expression);
  ContextModel cm = ContextModel();
  return exp.evaluate(EvaluationType.REAL, cm);
}
