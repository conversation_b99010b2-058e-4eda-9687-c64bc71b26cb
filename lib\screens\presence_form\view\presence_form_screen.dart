import 'package:city_academy/core/util/constants.dart';
import 'package:city_academy/core/util/global_instances.dart';
import 'package:city_academy/core/widgets/appbar_custom.dart';
import 'package:city_academy/core/widgets/buttons/custom_button.dart';
import 'package:city_academy/core/widgets/calendar_detail.dart';
import 'package:city_academy/core/widgets/checkbox_tile.dart';
import 'package:city_academy/core/widgets/dropdowns/dropdown_custom.dart';
import 'package:city_academy/core/widgets/snack_bar.dart';
import 'package:city_academy/routes/app_routes.dart';
import 'package:city_academy/routes/routes.dart';
import 'package:city_academy/screens/presence_form/controller/cubit/presence_form_state.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class PresenceFormScreen extends StatefulWidget {
  const PresenceFormScreen({super.key});

  @override
  State<PresenceFormScreen> createState() => _PresenceFormScreenState();
}

class _PresenceFormScreenState extends State<PresenceFormScreen> {
  final _formKey = GlobalKey<FormState>();

  @override
  void initState() {
    super.initState();
    presenceFormCubit.getCoaches();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: const AppbarCustom(title: 'Registro de presença'),
      body: BlocBuilder(
          bloc: presenceFormCubit,
          builder: (context, PresenceFormState state) {
            return SingleChildScrollView(
              child: Column(
                children: [
                  CalendarDetails(eventCity: state.eventCity),
                  const Divider(),
                  Form(
                    key: _formKey,
                    child: Padding(
                      padding: const EdgeInsets.all(36),
                      child: Column(
                        children: [
                          ...activityType.entries.map((entry) => Padding(
                                padding:
                                    const EdgeInsets.symmetric(vertical: 6),
                                child: CheckboxTile(
                                  title: entry.value,
                                  value: state.presenceFormModel.activityType ==
                                      entry.key,
                                  onChanged: (value) {
                                    if (value == true) {
                                      presenceFormCubit
                                          .onActivityTypeChanged(entry.key);
                                    }
                                  },
                                ),
                              )),
                          const SizedBox(height: 26),
                          DropdownCustom(
                            label: 'Profissional',
                            value: state.presenceFormModel.professional,
                            onChanged: (value) {
                              presenceFormCubit.onProfessionalChanged(value);
                            },
                            items: state.coaches
                                .map((coach) => DropdownMenuItem(
                                      value: coach.id,
                                      child: Text(coach.name),
                                    ))
                                .toList(),
                          ),
                          const SizedBox(height: 26),
                          DropdownCustom(
                            label: 'Público alvo',
                            value: state.presenceFormModel.targetAudience,
                            onChanged: (value) {
                              presenceFormCubit.onTargetAudienceChanged(value);
                            },
                            items: targetAudience.entries
                                .map((entry) => DropdownMenuItem(
                                      value: entry.key,
                                      child: Text(entry.value),
                                    ))
                                .toList(),
                          ),
                          const SizedBox(height: 26),
                          DropdownCustom(
                            label: 'Tema',
                            value: state.presenceFormModel.healthTopic,
                            onChanged: (value) {
                              presenceFormCubit.onHealthTopicChanged(value);
                            },
                            items: healthTopics.entries
                                .map((entry) => DropdownMenuItem(
                                      value: entry.key,
                                      child: Text(entry.value),
                                    ))
                                .toList(),
                          ),
                          const SizedBox(height: 26),
                          DropdownCustom(
                            label: 'Práticas',
                            value: state.presenceFormModel.healthProcedure,
                            onChanged: (value) {
                              presenceFormCubit.onHealthProcedureChanged(value);
                            },
                            items: healthProcedures.entries
                                .map((entry) => DropdownMenuItem(
                                      value: entry.key,
                                      child: Text(entry.value),
                                    ))
                                .toList(),
                          ),
                          const SizedBox(height: 26),
                          CustomButton(
                            title: 'Próximo',
                            onPressed: presenceFormCubit.isFormValid()
                                ? () {
                                    if (_formKey.currentState!.validate()) {
                                      presenceFormCubit.action();
                                      eventCityAdminCubit
                                          .setEventCity(state.eventCity);
                                      router.push(Routes.registerAttendance);
                                    } else {
                                      showErrorSnackbar(
                                          context, 'Preencha todos os campos');
                                    }
                                  }
                                : null,
                          )
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            );
          }),
    );
  }
}
