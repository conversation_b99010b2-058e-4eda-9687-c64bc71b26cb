import 'package:city_academy/screens/student_details/controller/bloc/student_details_state.dart';
import 'package:city_academy/screens/student_details/model/user_in_activity.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class StudentDetailsBloc extends Cubit<StudentDetailsState> {
  StudentDetailsBloc() : super(StudentDetailsStateEmpty());

  void setUser(UserInActivity value) => emit(state.copyWith(student: value));
}
