import 'package:city_academy/screens/student_details/controller/bloc/student_details_state.dart';
import 'package:city_academy/screens/student_details/controller/student_details_service.dart';
import 'package:city_academy/screens/student_details/model/user_in_activity.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class StudentDetailsBloc extends Cubit<StudentDetailsState> {
  StudentDetailsBloc() : super(StudentDetailsStateEmpty());

  void setUser(UserInActivity value) => emit(state.copyWith(student: value));

  Future<void> refreshStudentData() async {
    if (state.student.document == null || state.student.document!.isEmpty) {
      emit(state.copyWith(error: 'Documento inválido'));
      return;
    }

    emit(state.copyWith(loading: true));

    try {
      final result =
          await StudentDetailsService().call(state.student.document!);

      result.fold(
        (error) => emit(state.copyWith(error: error)),
        (updatedStudent) =>
            emit(state.copyWith(student: updatedStudent, error: null)),
      );
    } catch (e) {
      emit(state.copyWith(error: e.toString()));
    } finally {
      emit(state.copyWith(loading: false));
    }
  }
}
