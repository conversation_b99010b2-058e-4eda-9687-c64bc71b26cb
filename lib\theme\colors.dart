import 'package:flutter/material.dart';

class CustomColors {
  // Tons de Verde
  static const Color greenDark = Color(0xFF19972C);
  static const Color greenBase = Color(0xFF19962C);
  static const Color greenLight = Color(0xFF11B163);

  // Tons Neutros & Cinzas
  static const Color neutralBlack = Color(0xFF000000);
  static const Color neutralDark900 = Color(0xFF1B2029);
  static const Color neutralDark800 = Color(0xFF2E2E2E);
  static const Color neutralDark700 = Color(0xFF464E5C);
  static const Color neutralGray600 = Color(0xFF898B8F);
  static const Color neutralGray500 = Color(0xFFBEBAB3);
  static const Color neutralGray400 = Color(0xFFC1BAB5);
  static const Color neutralGray300 = Color(0xFFDBDBDB);
  static const Color neutralGray200 = Color(0xFFDFDFDF);
  static const Color neutralGray100 = Color(0xFFF2F5F7);
  static const Color neutralWhite = Color(0xFFFFFFFF);

  // Tons Quentes (Amarelo, Vermelho, Bege)
  static const Color warningYellow = Color(0xFFFFBC00);
  static const Color errorRed = Color(0xFFDB4437);
  static const Color accentSoftRed = Color(0xFFF6D0CD);
  static const Color backgroundBeige = Color(0xFFF8F2EE);

  // Tons de Azul
  static const Color primaryBlue = Color(0xFF3E82F4);
  static const Color primaryBlueDark = Color(0xFF173DB7);
  static const Color primaryBlueLight = Color(0xFF008AF4);
  static const Color neutralGrayBlue300 = Color(0xFFCCD2E3);

  // Outros
  static const Color accentLime = Color(0xFFCEDF50);
  static const Color backgroundSoftBeige = Color(0xFFF7F2EE);
  static const Color neutralGray50 = Color(0xFFF5F5F5);
  static const Color neutralDark850 = Color(0xFF2D2D2D);
}
