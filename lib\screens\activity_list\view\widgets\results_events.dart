import 'package:city_academy/core/util/global_instances.dart';
import 'package:city_academy/screens/activity_list/controller/bloc/activity_list_state.dart';
import 'package:city_academy/screens/activity_list/view/widgets/list_events.dart';
import 'package:city_academy/theme/colors.dart';
import 'package:city_academy/theme/texts.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class ResultsEvents extends StatelessWidget {
  const ResultsEvents({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocBuilder(
      bloc: activityListCubit,
      builder: (context, ActivityListState state) {
        if (state.isLoading) {
          return const Center(
            child: CircularProgressIndicator(color: CustomColors.greenBase),
          );
        }
        if (state.activityList.isNotEmpty) {
          return Expanded(
            child: ListEvents(
              activityList: state.activityList,
              onRefresh: () async => activityListCubit.fetchActivities(),
              activityType: state.activityType,
            ),
          );
        }
        return Expanded(
          child: Center(
            child: Text(
              'Não temos eventos para este dia',
              style: titleText20.copyWith(fontWeight: FontWeight.w500),
            ),
          ),
        );
      },
    );
  }
}
