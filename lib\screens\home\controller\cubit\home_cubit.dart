import 'package:city_academy/core/util/global_instances.dart';
import 'package:city_academy/screens/home/<USER>/cubit/home_state.dart';
import 'package:city_academy/screens/home/<USER>/home_service.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class HomeCubit extends Cubit<HomeState> {
  HomeCubit() : super(HomeStateEmpty());

  Future<void> fetchActivityCenters() async {
    emit(state.copyWith(loading: true));

    try {
      final response = await HomeService().fetchActivityCenters();

      emit(state.copyWith(activityCenterList: response));
      await updateSelectedActivityCenter();
    } catch (e) {
      emit(state.copyWith(error: e.toString()));
    } finally {
      emit(state.copyWith(loading: false));
    }
  }

  Future<void> updateSelectedActivityCenter() async {
    final selectedActivityCenter = state.activityCenterList.firstWhere(
      (element) => element.id == loginCubit.state.user.activityCenterId,
      orElse: () => state.activityCenterList.first,
    );

    activityListCubit.updateCenterId(selectedActivityCenter.id);
    emit(state.copyWith(selectedActivityCenter: selectedActivityCenter));
  }
}
