import 'package:city_academy/core/util/global_instances.dart';
import 'package:city_academy/core/widgets/dropdowns/dropdown_custom.dart';
import 'package:city_academy/screens/form/controller/cubit/form_state.dart';
import 'package:city_academy/screens/form/models/form_field_model.dart';
import 'package:flutter/material.dart';

Widget buildSelect(
  FormFieldModel field,
  FormScreenState state,
  bool isEnabled,
) {
  List<dynamic> options = [];
  if (field.options is List) {
    options = field.options as List<dynamic>;
  } else if (field.options is Map) {
    final mapOptions = field.options as Map<String, dynamic>;
    options = mapOptions.entries
        .map((e) => {
              'value': e.key,
              'label': e.value,
            })
        .toList();
  }

  final isEnabled =
      field.name == 'unidade_saude' ? state.isHealthUnitEnabled : field.canEdit;

  final currentValue = state.formValues[field.name].toString();
  final currentOptions =
      field.name == 'unidade_saude' ? state.healthUnitOptions : options;

  final isValidValue = currentOptions.any(
    (option) => option['value'] == currentValue,
  );

  return Padding(
    padding: const EdgeInsets.only(bottom: 16.0),
    child: DropdownCustom(
      label: field.label,
      isEnabled: !isEnabled ? false : isEnabled,
      value: isValidValue ? currentValue : null,
      items: currentOptions.map((option) {
        return DropdownMenuItem<String>(
          value: option['value'] as String?,
          child: Text(option['label'] as String),
        );
      }).toList(),
      onChanged: (value) {
        if (value != null) {
          if (field.name == 'distrito_residencia') {
            formCubit.updateDistrictSelection(value);
          }
          formCubit.updateFieldValue(field.name, value);
        }
      },
    ),
  );
}
