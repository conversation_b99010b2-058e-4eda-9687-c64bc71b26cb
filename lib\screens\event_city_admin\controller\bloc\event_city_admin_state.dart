import 'package:city_academy/core/shared/base_state.dart';
import 'package:city_academy/core/util/enums.dart';
import 'package:city_academy/screens/event_city_admin/model/event_city_admin_model.dart';
import 'package:city_academy/screens/student_details/model/user_in_activity.dart';
import 'package:copy_with_extension/copy_with_extension.dart';
import 'package:empty_annotation/empty_annotation.dart';

part 'event_city_admin_state.g.dart';

@Empty()
@CopyWith()
class EventCityAdminState extends BaseState {
  final List<String> listSelected;
  final EventCityAdminModel eventCity;
  final String activityId;
  final List<UserInActivity> users;
  final EventCityAdminStatus status;

  EventCityAdminState({
    required super.loading,
    required super.error,
    required super.message,
    required this.listSelected,
    required this.eventCity,
    required this.activityId,
    required this.users,
    required this.status,
  });
}
