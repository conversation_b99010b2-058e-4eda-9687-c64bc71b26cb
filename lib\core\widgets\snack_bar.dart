import 'package:city_academy/theme/colors.dart';
import 'package:city_academy/theme/texts.dart';
import 'package:flutter/material.dart';

void showCustomSnackbar(BuildContext context, String message) {
  ScaffoldMessenger.of(context).showSnackBar(CustomSnackBar(message: message));
}

void showErrorSnackbar(BuildContext context, String message) {
  ScaffoldMessenger.of(context).showSnackBar(CustomSnackBar(
    message: message,
    isError: true,
  ));
}

class CustomSnackBar extends SnackBar {
  final String message;
  final bool isError;
  CustomSnackBar({
    required this.message,
    this.isError = false,
    super.key,
  }) : super(
          content: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Padding(
                padding:
                    const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                child: Text(
                  message,
                  style: titleText14.copyWith(
                    color: CustomColors.neutralWhite,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
            ],
          ),
          behavior: SnackBarBehavior.floating,
          backgroundColor:
              isError ? CustomColors.errorRed : CustomColors.greenDark,
          elevation: 6,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
            side: const BorderSide(
              color: CustomColors.neutralGray400,
              width: 2,
            ),
          ),
          showCloseIcon: true,
          width: 343,
          closeIconColor: CustomColors.neutralWhite,
        );
}
