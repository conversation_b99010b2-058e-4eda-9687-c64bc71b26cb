// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'form_data.dart';

// **************************************************************************
// CopyWithGenerator
// **************************************************************************

abstract class _$FormDataCWProxy {
  /// This function **does support** nullification of nullable fields. All `null` values passed to `non-nullable` fields will be ignored.
  ///
  /// Usage
  /// ```dart
  /// FormData(...).copyWith(id: 12, name: "My name")
  /// ````
  FormData call({
    String title,
    String? description,
    List<FormSection> sections,
    DateTime? createdAt,
    FormStatus? status,
  });
}

/// Proxy class for `copyWith` functionality. This is a callable class and can be used as follows: `instanceOfFormData.copyWith(...)`.
class _$FormDataCWProxyImpl implements _$FormDataCWProxy {
  const _$FormDataCWProxyImpl(this._value);

  final FormData _value;

  @override

  /// This function **does support** nullification of nullable fields. All `null` values passed to `non-nullable` fields will be ignored.
  ///
  /// Usage
  /// ```dart
  /// FormData(...).copyWith(id: 12, name: "My name")
  /// ````
  FormData call({
    Object? title = const $CopyWithPlaceholder(),
    Object? description = const $CopyWithPlaceholder(),
    Object? sections = const $CopyWithPlaceholder(),
    Object? createdAt = const $CopyWithPlaceholder(),
    Object? status = const $CopyWithPlaceholder(),
  }) {
    return FormData(
      title: title == const $CopyWithPlaceholder()
          ? _value.title
          // ignore: cast_nullable_to_non_nullable
          : title as String,
      description: description == const $CopyWithPlaceholder()
          ? _value.description
          // ignore: cast_nullable_to_non_nullable
          : description as String?,
      sections: sections == const $CopyWithPlaceholder()
          ? _value.sections
          // ignore: cast_nullable_to_non_nullable
          : sections as List<FormSection>,
      createdAt: createdAt == const $CopyWithPlaceholder()
          ? _value.createdAt
          // ignore: cast_nullable_to_non_nullable
          : createdAt as DateTime?,
      status: status == const $CopyWithPlaceholder()
          ? _value.status
          // ignore: cast_nullable_to_non_nullable
          : status as FormStatus?,
    );
  }
}

extension $FormDataCopyWith on FormData {
  /// Returns a callable class that can be used as follows: `instanceOfFormData.copyWith(...)`.
  // ignore: library_private_types_in_public_api
  _$FormDataCWProxy get copyWith => _$FormDataCWProxyImpl(this);

  /// Copies the object with the specific fields set to `null`. If you pass `false` as a parameter, nothing will be done and it will be ignored. Don't do it. Prefer `copyWith(field: null)`.
  ///
  /// Usage
  /// ```dart
  /// FormData(...).copyWithNull(firstField: true, secondField: true)
  /// ````
  FormData copyWithNull({
    bool description = false,
    bool createdAt = false,
    bool status = false,
  }) {
    return FormData(
      title: title,
      description: description == true ? null : this.description,
      sections: sections,
      createdAt: createdAt == true ? null : this.createdAt,
      status: status == true ? null : this.status,
    );
  }
}
