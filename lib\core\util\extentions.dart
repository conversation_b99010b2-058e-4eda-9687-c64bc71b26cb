import 'package:city_academy/core/util/enums.dart';

extension UpperLetter on String {
  String upperOnlyFirstLetter() {
    if (isEmpty) return '';
    return "${this[0].toUpperCase()}${substring(1).toLowerCase()}";
  }
}

extension DateShow on DateTime {
  String formatExibition() {
    return "${day.toString().padLeft(2, '0')}/${month.toString().padLeft(2, '0')}/$year";
  }

  String getCustomHour() {
    final hours = toIso8601String().split(r'T')[1].split(r':');
    hours.removeLast();
    return hours.join(':');
  }

  String getCustomDate({bool withYear = false}) {
    if (withYear) {
      return toIso8601String().split(r'T')[0].split('-').reversed.join('/');
    }
    return toIso8601String()
        .split(r'T')[0]
        .substring(5)
        .split('-')
        .reversed
        .join('/');
  }

  String formatDateHour({bool withAt = false}) {
    // 1969-07-20T20:18:04.000Z
    final textDate = toIso8601String();
    final date = textDate.split(r'T')[0].split(r'-').reversed.join('/');
    final hour = textDate.split(r'T')[1].split(r':').take(2).join(':');
    return '$date ${withAt ? 'ás' : ''} $hour';
  }
}

extension StringExtension on String {
  String nameImprovident() {
    if (trim().split(' ').length > 1) {
      final splinted = trim().split(' ');
      return '${splinted.first} ${splinted.last}';
    }
    return this;
  }
}

extension GenderStringRepresentation on UserGender {
  String toStringRepresentation() {
    switch (this) {
      case UserGender.male:
        return 'masculino';

      case UserGender.female:
        return 'feminino';
    }
  }
}

extension GenderByString on String {
  UserGender toGender() {
    switch (toLowerCase()) {
      case 'male':
        return UserGender.male;
      case 'female':
        return UserGender.female;
      default:
        return UserGender.male;
    }
  }
}

extension ActivityUserStatus on String {
  StatusInActivity toStatus() {
    switch (toLowerCase()) {
      case 'checkin_will_pass':
        return StatusInActivity.confirmed;
      case 'wait_list_will_pass':
        return StatusInActivity.waitList;
      case 'wait_list_already_passed':
        return StatusInActivity.waitList;
      case 'checkin_already_passed':
        return StatusInActivity.confirmed;
      case 'cancelled':
        return StatusInActivity.cancelled;
      default:
        return StatusInActivity.cancelled;
    }
  }
}

extension StringNullableExtension on String? {
  bool isNullOrEmpty() {
    return this == null || (this?.isEmpty ?? false);
  }
}

extension ActivityTypeExtension on String {
  ActivityType toActivityType() {
    switch (toLowerCase()) {
      case 'evaluation':
        return ActivityType.avaliation;
      case 'physical_activity':
        return ActivityType.activity;
      case 'meeting':
        return ActivityType.meet;
      default:
        return ActivityType.avaliation;
    }
  }
}

extension ActivityTypeString on ActivityType {
  String toStringRepresentation() {
    switch (this) {
      case ActivityType.avaliation:
        return 'evaluation';
      case ActivityType.activity:
        return 'physical_activity';
      case ActivityType.meet:
        return 'meeting';
    }
  }
}
