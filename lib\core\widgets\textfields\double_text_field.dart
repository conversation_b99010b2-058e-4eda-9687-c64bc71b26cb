import 'package:city_academy/core/widgets/textfields/text_field_custom.dart';
import 'package:flutter/services.dart';

class DoubleTextField extends TextFieldCustom {
  DoubleTextField({
    super.key,
    super.onChanged,
    super.errorMessage,
    super.hintText,
    super.textInputAction,
    super.initialValue,
    super.controller,
    super.validator,
    super.isEnabled,
    List<TextInputFormatter>? inputFormatters,
  }) : super(
          keyboardType: const TextInputType.numberWithOptions(decimal: true),
          inputFormatters: [
            if (inputFormatters != null) ...inputFormatters,
            FilteringTextInputFormatter.allow(
              RegExp(r'^\d*([.,]\d*)?$'),
            ),
          ],
        );
}
