// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'user_in_activity.dart';

// **************************************************************************
// CopyWithGenerator
// **************************************************************************

abstract class _$UserInActivityCWProxy {
  /// This function **does support** nullification of nullable fields. All `null` values passed to `non-nullable` fields will be ignored.
  ///
  /// Usage
  /// ```dart
  /// UserInActivity(...).copyWith(id: 12, name: "My name")
  /// ````
  UserInActivity call({
    String? emergencyContact,
    String? relashionship,
    bool? isComplete,
    Map<String, bool>? diseases,
    bool? hasConfirmation,
    String id,
    String sub,
    String type,
    bool active,
    DateTime createdAt,
    DateTime updatedAt,
    String profileImageId,
    String firstName,
    String lastName,
    String nickname,
    String email,
    String phone,
    UserGender gender,
    String genderIdentity,
    String race,
    String sexualOrientation,
    DateTime birthDate,
    Map<String, dynamic>? address,
    List<dynamic>? contacts,
    String? activityCenterId,
    String? document,
    String? cns,
    String? cref,
  });
}

/// Proxy class for `copyWith` functionality. This is a callable class and can be used as follows: `instanceOfUserInActivity.copyWith(...)`.
class _$UserInActivityCWProxyImpl implements _$UserInActivityCWProxy {
  const _$UserInActivityCWProxyImpl(this._value);

  final UserInActivity _value;

  @override

  /// This function **does support** nullification of nullable fields. All `null` values passed to `non-nullable` fields will be ignored.
  ///
  /// Usage
  /// ```dart
  /// UserInActivity(...).copyWith(id: 12, name: "My name")
  /// ````
  UserInActivity call({
    Object? emergencyContact = const $CopyWithPlaceholder(),
    Object? relashionship = const $CopyWithPlaceholder(),
    Object? isComplete = const $CopyWithPlaceholder(),
    Object? diseases = const $CopyWithPlaceholder(),
    Object? hasConfirmation = const $CopyWithPlaceholder(),
    Object? id = const $CopyWithPlaceholder(),
    Object? sub = const $CopyWithPlaceholder(),
    Object? type = const $CopyWithPlaceholder(),
    Object? active = const $CopyWithPlaceholder(),
    Object? createdAt = const $CopyWithPlaceholder(),
    Object? updatedAt = const $CopyWithPlaceholder(),
    Object? profileImageId = const $CopyWithPlaceholder(),
    Object? firstName = const $CopyWithPlaceholder(),
    Object? lastName = const $CopyWithPlaceholder(),
    Object? nickname = const $CopyWithPlaceholder(),
    Object? email = const $CopyWithPlaceholder(),
    Object? phone = const $CopyWithPlaceholder(),
    Object? gender = const $CopyWithPlaceholder(),
    Object? genderIdentity = const $CopyWithPlaceholder(),
    Object? race = const $CopyWithPlaceholder(),
    Object? sexualOrientation = const $CopyWithPlaceholder(),
    Object? birthDate = const $CopyWithPlaceholder(),
    Object? address = const $CopyWithPlaceholder(),
    Object? contacts = const $CopyWithPlaceholder(),
    Object? activityCenterId = const $CopyWithPlaceholder(),
    Object? document = const $CopyWithPlaceholder(),
    Object? cns = const $CopyWithPlaceholder(),
    Object? cref = const $CopyWithPlaceholder(),
  }) {
    return UserInActivity(
      emergencyContact: emergencyContact == const $CopyWithPlaceholder()
          ? _value.emergencyContact
          // ignore: cast_nullable_to_non_nullable
          : emergencyContact as String?,
      relashionship: relashionship == const $CopyWithPlaceholder()
          ? _value.relashionship
          // ignore: cast_nullable_to_non_nullable
          : relashionship as String?,
      hasCompletedRegister: isComplete == const $CopyWithPlaceholder()
          ? _value.hasCompletedRegister
          // ignore: cast_nullable_to_non_nullable
          : isComplete as bool?,
      diseases: diseases == const $CopyWithPlaceholder()
          ? _value.diseases
          // ignore: cast_nullable_to_non_nullable
          : diseases as Map<String, bool>?,
      hasConfirmation: hasConfirmation == const $CopyWithPlaceholder()
          ? _value.hasConfirmation
          // ignore: cast_nullable_to_non_nullable
          : hasConfirmation as bool?,
      id: id == const $CopyWithPlaceholder()
          ? _value.id
          // ignore: cast_nullable_to_non_nullable
          : id as String,
      sub: sub == const $CopyWithPlaceholder()
          ? _value.sub
          // ignore: cast_nullable_to_non_nullable
          : sub as String,
      type: type == const $CopyWithPlaceholder()
          ? _value.type
          // ignore: cast_nullable_to_non_nullable
          : type as String,
      active: active == const $CopyWithPlaceholder()
          ? _value.active
          // ignore: cast_nullable_to_non_nullable
          : active as bool,
      createdAt: createdAt == const $CopyWithPlaceholder()
          ? _value.createdAt
          // ignore: cast_nullable_to_non_nullable
          : createdAt as DateTime,
      updatedAt: updatedAt == const $CopyWithPlaceholder()
          ? _value.updatedAt
          // ignore: cast_nullable_to_non_nullable
          : updatedAt as DateTime,
      profileImageId: profileImageId == const $CopyWithPlaceholder()
          ? _value.profileImageId
          // ignore: cast_nullable_to_non_nullable
          : profileImageId as String,
      firstName: firstName == const $CopyWithPlaceholder()
          ? _value.firstName
          // ignore: cast_nullable_to_non_nullable
          : firstName as String,
      lastName: lastName == const $CopyWithPlaceholder()
          ? _value.lastName
          // ignore: cast_nullable_to_non_nullable
          : lastName as String,
      nickname: nickname == const $CopyWithPlaceholder()
          ? _value.nickname
          // ignore: cast_nullable_to_non_nullable
          : nickname as String,
      email: email == const $CopyWithPlaceholder()
          ? _value.email
          // ignore: cast_nullable_to_non_nullable
          : email as String,
      phone: phone == const $CopyWithPlaceholder()
          ? _value.phone
          // ignore: cast_nullable_to_non_nullable
          : phone as String,
      gender: gender == const $CopyWithPlaceholder()
          ? _value.gender
          // ignore: cast_nullable_to_non_nullable
          : gender as UserGender,
      genderIdentity: genderIdentity == const $CopyWithPlaceholder()
          ? _value.genderIdentity
          // ignore: cast_nullable_to_non_nullable
          : genderIdentity as String,
      race: race == const $CopyWithPlaceholder()
          ? _value.race
          // ignore: cast_nullable_to_non_nullable
          : race as String,
      sexualOrientation: sexualOrientation == const $CopyWithPlaceholder()
          ? _value.sexualOrientation
          // ignore: cast_nullable_to_non_nullable
          : sexualOrientation as String,
      birthDate: birthDate == const $CopyWithPlaceholder()
          ? _value.birthDate
          // ignore: cast_nullable_to_non_nullable
          : birthDate as DateTime,
      address: address == const $CopyWithPlaceholder()
          ? _value.address
          // ignore: cast_nullable_to_non_nullable
          : address as Map<String, dynamic>?,
      contacts: contacts == const $CopyWithPlaceholder()
          ? _value.contacts
          // ignore: cast_nullable_to_non_nullable
          : contacts as List<dynamic>?,
      activityCenterId: activityCenterId == const $CopyWithPlaceholder()
          ? _value.activityCenterId
          // ignore: cast_nullable_to_non_nullable
          : activityCenterId as String?,
      document: document == const $CopyWithPlaceholder()
          ? _value.document
          // ignore: cast_nullable_to_non_nullable
          : document as String?,
      cns: cns == const $CopyWithPlaceholder()
          ? _value.cns
          // ignore: cast_nullable_to_non_nullable
          : cns as String?,
      cref: cref == const $CopyWithPlaceholder()
          ? _value.cref
          // ignore: cast_nullable_to_non_nullable
          : cref as String?,
    );
  }
}

extension $UserInActivityCopyWith on UserInActivity {
  /// Returns a callable class that can be used as follows: `instanceOfUserInActivity.copyWith(...)`.
  // ignore: library_private_types_in_public_api
  _$UserInActivityCWProxy get copyWith => _$UserInActivityCWProxyImpl(this);

  /// Copies the object with the specific fields set to `null`. If you pass `false` as a parameter, nothing will be done and it will be ignored. Don't do it. Prefer `copyWith(field: null)`.
  ///
  /// Usage
  /// ```dart
  /// UserInActivity(...).copyWithNull(firstField: true, secondField: true)
  /// ````
  UserInActivity copyWithNull({
    bool emergencyContact = false,
    bool relashionship = false,
    bool isComplete = false,
    bool diseases = false,
    bool hasConfirmation = false,
    bool address = false,
    bool contacts = false,
    bool activityCenterId = false,
    bool document = false,
    bool cns = false,
    bool cref = false,
  }) {
    return UserInActivity(
      emergencyContact: emergencyContact == true ? null : this.emergencyContact,
      relashionship: relashionship == true ? null : this.relashionship,
      hasCompletedRegister:
          isComplete == true ? null : this.hasCompletedRegister,
      diseases: diseases == true ? null : this.diseases,
      hasConfirmation: hasConfirmation == true ? null : this.hasConfirmation,
      id: id,
      sub: sub,
      type: type,
      active: active,
      createdAt: createdAt,
      updatedAt: updatedAt,
      profileImageId: profileImageId,
      firstName: firstName,
      lastName: lastName,
      nickname: nickname,
      email: email,
      phone: phone,
      gender: gender,
      genderIdentity: genderIdentity,
      race: race,
      sexualOrientation: sexualOrientation,
      birthDate: birthDate,
      address: address == true ? null : this.address,
      contacts: contacts == true ? null : this.contacts,
      activityCenterId: activityCenterId == true ? null : this.activityCenterId,
      document: document == true ? null : this.document,
      cns: cns == true ? null : this.cns,
      cref: cref == true ? null : this.cref,
    );
  }
}

// **************************************************************************
// EmptyGenerator
// **************************************************************************

class UserInActivityEmpty extends UserInActivity {
  UserInActivityEmpty()
      : super(
            emergencyContact: null,
            relashionship: null,
            hasCompletedRegister: null,
            diseases: null,
            hasConfirmation: null,
            id: '',
            sub: '',
            type: '',
            active: false,
            createdAt: DateTime.now(),
            updatedAt: DateTime.now(),
            profileImageId: '',
            firstName: '',
            lastName: '',
            nickname: '',
            email: '',
            phone: '',
            gender: UserGender.values.first,
            genderIdentity: '',
            race: '',
            sexualOrientation: '',
            birthDate: DateTime.now(),
            address: null,
            contacts: null,
            activityCenterId: null,
            document: null,
            cns: null,
            cref: null);
}
