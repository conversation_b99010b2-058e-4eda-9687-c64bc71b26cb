import 'package:city_academy/core/api/exceptions.dart';
import 'package:city_academy/core/util/app_texts.dart';
import 'package:city_academy/core/util/global_instances.dart';
import 'package:flutter/foundation.dart';

class AuthService {
  static final AuthService _instance = AuthService._internal();
  factory AuthService() => _instance;
  AuthService._internal();

  bool _isAuthenticated = false;
  bool get isAuthenticated => _isAuthenticated;

  Future<bool> checkAuthentication({bool forceCheck = false}) async {
    try {
      // Se já está autenticado e não é forçado, retorna true
      if (_isAuthenticated && !forceCheck) {
        debugPrint('Já autenticado localmente, retornando true');
        return true;
      }

      final idToken = await storage.get<String>(AppTexts.idTokenKey);
      final refreshToken = await storage.get<String>(AppTexts.refreshTokenKey);

      if (idToken == null || refreshToken == null) {
        debugPrint('Tokens não encontrados, usuário não autenticado');
        _isAuthenticated = false;
        return false;
      }

      // Se não é forçado e temos tokens, podemos assumir que está autenticado
      if (!forceCheck) {
        debugPrint('Tokens encontrados, assumindo autenticação válida');
        _isAuthenticated = true;
        return true;
      }

      // Se é forçado, faz a verificação real com a API
      try {
        debugPrint(
            'Verificação forçada - fazendo requisição para: ${AppTexts.getCoachInfo}');
        final response = await authClient.get(path: AppTexts.getCoachInfo);
        debugPrint('Autenticação verificada com sucesso: ${response.status}');
        _isAuthenticated = true;
        return true;
      } on UnauthorizedException catch (e) {
        debugPrint('Token expirado, tentando renovar... Erro: ${e.message}');
        return await this.refreshToken();
      } catch (e) {
        debugPrint('Erro ao verificar autenticação: $e');
        _isAuthenticated = false;
        return false;
      }
    } catch (e) {
      debugPrint('Erro ao verificar autenticação: $e');
      _isAuthenticated = false;
      return false;
    }
  }

  /// Força uma verificação de autenticação com a API
  Future<bool> forceCheckAuthentication() async {
    return await checkAuthentication(forceCheck: true);
  }

  /// Inicializa o estado de autenticação baseado nos tokens armazenados
  Future<void> initializeAuthState() async {
    final idToken = await storage.get<String>(AppTexts.idTokenKey);
    final refreshToken = await storage.get<String>(AppTexts.refreshTokenKey);

    _isAuthenticated = idToken != null && refreshToken != null;
    debugPrint('Estado de autenticação inicializado: $_isAuthenticated');
  }

  Future<bool> refreshToken() async {
    try {
      final refreshTokenValue =
          await storage.get<String>(AppTexts.refreshTokenKey);
      if (refreshTokenValue == null) {
        _isAuthenticated = false;
        return false;
      }

      final response = await unAuthClient.post(
        path: AppTexts.refreshToken,
        body: {'refresh_token': refreshTokenValue},
      );

      await storage.setAll({
        AppTexts.idTokenKey: response.data['id_token'],
        AppTexts.refreshTokenKey: response.data['refresh_token'],
      });

      _isAuthenticated = true;
      debugPrint('Token renovado com sucesso');
      return true;
    } catch (e) {
      debugPrint('Erro ao renovar token: $e');
      await _clearTokens();
      clearAuthCache();
      return false;
    }
  }

  Future<void> _clearTokens() async {
    await storage.deleteAll({
      AppTexts.idTokenKey,
      AppTexts.refreshTokenKey,
      AppTexts.localId,
    });
  }

  Future<void> logout() async {
    await _clearTokens();
    _isAuthenticated = false;
    debugPrint('Logout realizado, cache de autenticação limpo');
  }

  /// Limpa o cache de autenticação (útil quando tokens podem ter expirado)
  void clearAuthCache() {
    _isAuthenticated = false;
    debugPrint('Cache de autenticação limpo');
  }
}
