// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'health_indicators_state.dart';

// **************************************************************************
// CopyWithGenerator
// **************************************************************************

abstract class _$HealthIndicatorsStateCWProxy {
  /// This function **does support** nullification of nullable fields. All `null` values passed to `non-nullable` fields will be ignored.
  ///
  /// Usage
  /// ```dart
  /// HealthIndicatorsState(...).copyWith(id: 12, name: "My name")
  /// ````
  HealthIndicatorsState call({
    List<MeasurementData> historicalData,
    bool loading,
    String error,
    String message,
  });
}

/// Proxy class for `copyWith` functionality. This is a callable class and can be used as follows: `instanceOfHealthIndicatorsState.copyWith(...)`.
class _$HealthIndicatorsStateCWProxyImpl
    implements _$HealthIndicatorsStateCWProxy {
  const _$HealthIndicatorsStateCWProxyImpl(this._value);

  final HealthIndicatorsState _value;

  @override

  /// This function **does support** nullification of nullable fields. All `null` values passed to `non-nullable` fields will be ignored.
  ///
  /// Usage
  /// ```dart
  /// HealthIndicatorsState(...).copyWith(id: 12, name: "My name")
  /// ````
  HealthIndicatorsState call({
    Object? historicalData = const $CopyWithPlaceholder(),
    Object? loading = const $CopyWithPlaceholder(),
    Object? error = const $CopyWithPlaceholder(),
    Object? message = const $CopyWithPlaceholder(),
  }) {
    return HealthIndicatorsState(
      historicalData: historicalData == const $CopyWithPlaceholder()
          ? _value.historicalData
          // ignore: cast_nullable_to_non_nullable
          : historicalData as List<MeasurementData>,
      loading: loading == const $CopyWithPlaceholder()
          ? _value.loading
          // ignore: cast_nullable_to_non_nullable
          : loading as bool,
      error: error == const $CopyWithPlaceholder()
          ? _value.error
          // ignore: cast_nullable_to_non_nullable
          : error as String,
      message: message == const $CopyWithPlaceholder()
          ? _value.message
          // ignore: cast_nullable_to_non_nullable
          : message as String,
    );
  }
}

extension $HealthIndicatorsStateCopyWith on HealthIndicatorsState {
  /// Returns a callable class that can be used as follows: `instanceOfHealthIndicatorsState.copyWith(...)`.
  // ignore: library_private_types_in_public_api
  _$HealthIndicatorsStateCWProxy get copyWith =>
      _$HealthIndicatorsStateCWProxyImpl(this);
}

// **************************************************************************
// EmptyGenerator
// **************************************************************************

class HealthIndicatorsStateEmpty extends HealthIndicatorsState {
  HealthIndicatorsStateEmpty()
      : super(historicalData: const [], loading: false, error: '', message: '');
}
