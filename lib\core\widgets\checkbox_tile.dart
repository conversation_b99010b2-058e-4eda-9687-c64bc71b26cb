import 'package:city_academy/core/widgets/custom_checkbox.dart';
import 'package:city_academy/theme/colors.dart';
import 'package:city_academy/theme/texts.dart';
import 'package:flutter/material.dart';

class CheckboxTile extends StatefulWidget {
  final bool value;
  final ValueChanged<bool?>? onChanged;
  final String title;

  const CheckboxTile({
    super.key,
    required this.value,
    required this.onChanged,
    required this.title,
  });

  @override
  CheckboxTileState createState() => CheckboxTileState();
}

class CheckboxTileState extends State<CheckboxTile> {
  @override
  Widget build(BuildContext context) {
    return Row(children: [
      CustomCheckbox(
        value: widget.value,
        onChanged: widget.onChanged,
      ),
      const SizedBox(width: 8),
      Text(widget.title,
          style: titleText14.copyWith(
            color: CustomColors.neutralDark700,
            fontWeight: FontWeight.w400,
          )),
    ]);
  }
}
