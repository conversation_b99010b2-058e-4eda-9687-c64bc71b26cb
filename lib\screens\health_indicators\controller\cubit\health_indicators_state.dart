import 'package:city_academy/core/shared/base_state.dart';
import 'package:city_academy/screens/health_indicators/model/measurement_data.dart';
import 'package:copy_with_extension/copy_with_extension.dart';
import 'package:empty_annotation/empty_annotation.dart';

part 'health_indicators_state.g.dart';

@CopyWith()
@Empty()
class HealthIndicatorsState extends BaseState {
  final List<MeasurementData> historicalData;

  HealthIndicatorsState({
    required this.historicalData,
    required super.loading,
    required super.error,
    required super.message,
  });
}
