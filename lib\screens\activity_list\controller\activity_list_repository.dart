import 'package:city_academy/core/util/app_texts.dart';
import 'package:city_academy/core/util/global_instances.dart';
import 'package:city_academy/screens/activity_list/model/activity_model.dart';
import 'package:city_academy/screens/activity_list/model/activity_search_model.dart';

class ActivityListRepository {
  Future<List<ActivityModel>> fetchActivities(ActivitySearchModel model) async {
    final response = await authClient.get(
      path: AppTexts.activityList(model.centerId),
      query: model.toJson(),
    );

    return (response.data['items'] as List)
        .map((e) => ActivityModel.fromJson(e))
        .toList();
  }
}
