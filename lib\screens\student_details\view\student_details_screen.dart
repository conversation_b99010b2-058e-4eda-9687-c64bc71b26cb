import 'package:city_academy/core/util/global_instances.dart';
import 'package:city_academy/core/util/image_path.dart';
import 'package:city_academy/core/widgets/appbar_custom.dart';
import 'package:city_academy/core/widgets/buttons/info_row_button.dart';
import 'package:city_academy/core/widgets/custom_divider.dart';
import 'package:city_academy/core/widgets/profile_tile.dart';
import 'package:city_academy/routes/app_routes.dart';
import 'package:city_academy/routes/routes.dart';
import 'package:city_academy/screens/student_details/controller/bloc/student_details_state.dart';
import 'package:city_academy/screens/student_details/model/user_converter.dart';
import 'package:city_academy/theme/colors.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class StudentDetailsScreen extends StatefulWidget {
  const StudentDetailsScreen({super.key});

  @override
  State<StudentDetailsScreen> createState() => _StudentDetailsScreenState();
}

class _StudentDetailsScreenState extends State<StudentDetailsScreen> {
  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      formCubit.reset();
      historyCubit.reset();
      healthIndicatorsCubit.reset();
      studentDetailsBloc.refreshStudentData();
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        appBar: const AppbarCustom(title: 'Perfil'),
        body: BlocBuilder(
          bloc: studentDetailsBloc,
          builder: (context, StudentDetailsState state) {
            if (state.loading) {
              return const Center(child: CircularProgressIndicator());
            }
            return SingleChildScrollView(
              child: Padding(
                padding: const EdgeInsets.symmetric(horizontal: 16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const SizedBox(height: 26),
                    ProfileTile(user: state.student),
                    const SizedBox(height: 26),
                    const CustomDivider(),
                    InfoRowButton(
                      onTap: () {
                        formCubit.setUser(UserConverter.toUser(state.student));
                        return router.push(Routes.registerInfo);
                      },
                      leadingIcon: ImagePath.userInfo,
                      leadingIconColor: state.student.isComplete ?? false
                          ? CustomColors.greenBase
                          : CustomColors.errorRed,
                      title: 'Informações de cadastro',
                    ),
                    const CustomDivider(),
                    InfoRowButton(
                      onTap: () => router.push(Routes.historyAssessments),
                      leadingIcon: ImagePath.addFile,
                      leadingIconColor: CustomColors.neutralGray300,
                      title: 'Informações da avaliação',
                    ),
                    const CustomDivider(),
                    InfoRowButton(
                      onTap: () => router.push(Routes.historyMonitorment),
                      leadingIcon: ImagePath.monitoringHealth,
                      leadingIconColor:
                          state.student.diseases?.isNotEmpty ?? false
                              ? CustomColors.warningYellow
                              : CustomColors.neutralGray300,
                      title: 'Monitoramento HAS e DM',
                    ),
                    const CustomDivider(),
                    InfoRowButton(
                      onTap: () => router.push(Routes.healthIndicators),
                      leadingIcon: ImagePath.graph,
                      title: 'Indicadores de saúde',
                    ),
                    const CustomDivider(),
                  ],
                ),
              ),
            );
          },
        ));
  }
}
