import 'package:city_academy/core/shared/base_service.dart';
import 'package:city_academy/screens/event_city_admin/model/event_city_admin_model.dart';
import 'package:city_academy/screens/presence_form/controller/presence_form_repository.dart';

class PresenceFormService extends BaseService {
  final _repository = PresenceFormRepository();

  Future<EventCityAdminModel> getActivityDetails(
    String centerId,
    String activityId,
  ) {
    return super
        .call(() => _repository.getActivityDetails(centerId, activityId));
  }

  Future<EventCityAdminModel> updateActivityDetails(EventCityAdminModel model) {
    return super.call(() => _repository.updateActivityDetails(model));
  }
}
