// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'search_students_state.dart';

// **************************************************************************
// CopyWithGenerator
// **************************************************************************

abstract class _$SearchStudentsStateCWProxy {
  /// This function **does support** nullification of nullable fields. All `null` values passed to `non-nullable` fields will be ignored.
  ///
  /// Usage
  /// ```dart
  /// SearchStudentsState(...).copyWith(id: 12, name: "My name")
  /// ````
  SearchStudentsState call({
    List<UserInActivity>? students,
    String? error,
    bool loading,
    SearchType type,
    String query,
  });
}

/// Proxy class for `copyWith` functionality. This is a callable class and can be used as follows: `instanceOfSearchStudentsState.copyWith(...)`.
class _$SearchStudentsStateCWProxyImpl implements _$SearchStudentsStateCWProxy {
  const _$SearchStudentsStateCWProxyImpl(this._value);

  final SearchStudentsState _value;

  @override

  /// This function **does support** nullification of nullable fields. All `null` values passed to `non-nullable` fields will be ignored.
  ///
  /// Usage
  /// ```dart
  /// SearchStudentsState(...).copyWith(id: 12, name: "My name")
  /// ````
  SearchStudentsState call({
    Object? students = const $CopyWithPlaceholder(),
    Object? error = const $CopyWithPlaceholder(),
    Object? loading = const $CopyWithPlaceholder(),
    Object? type = const $CopyWithPlaceholder(),
    Object? query = const $CopyWithPlaceholder(),
  }) {
    return SearchStudentsState(
      students: students == const $CopyWithPlaceholder()
          ? _value.students
          // ignore: cast_nullable_to_non_nullable
          : students as List<UserInActivity>?,
      error: error == const $CopyWithPlaceholder()
          ? _value.error
          // ignore: cast_nullable_to_non_nullable
          : error as String?,
      loading: loading == const $CopyWithPlaceholder()
          ? _value.loading
          // ignore: cast_nullable_to_non_nullable
          : loading as bool,
      type: type == const $CopyWithPlaceholder()
          ? _value.type
          // ignore: cast_nullable_to_non_nullable
          : type as SearchType,
      query: query == const $CopyWithPlaceholder()
          ? _value.query
          // ignore: cast_nullable_to_non_nullable
          : query as String,
    );
  }
}

extension $SearchStudentsStateCopyWith on SearchStudentsState {
  /// Returns a callable class that can be used as follows: `instanceOfSearchStudentsState.copyWith(...)`.
  // ignore: library_private_types_in_public_api
  _$SearchStudentsStateCWProxy get copyWith =>
      _$SearchStudentsStateCWProxyImpl(this);

  /// Copies the object with the specific fields set to `null`. If you pass `false` as a parameter, nothing will be done and it will be ignored. Don't do it. Prefer `copyWith(field: null)`.
  ///
  /// Usage
  /// ```dart
  /// SearchStudentsState(...).copyWithNull(firstField: true, secondField: true)
  /// ````
  SearchStudentsState copyWithNull({
    bool students = false,
    bool error = false,
  }) {
    return SearchStudentsState(
      students: students == true ? null : this.students,
      error: error == true ? null : this.error,
      loading: loading,
      type: type,
      query: query,
    );
  }
}

// **************************************************************************
// EmptyGenerator
// **************************************************************************

class SearchStudentsStateEmpty extends SearchStudentsState {
  SearchStudentsStateEmpty()
      : super(
            students: null,
            error: null,
            loading: false,
            type: SearchType.values.first,
            query: '');
}
