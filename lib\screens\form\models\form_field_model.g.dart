// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'form_field_model.dart';

// **************************************************************************
// CopyWithGenerator
// **************************************************************************

abstract class _$FormFieldModelCWProxy {
  /// This function **does support** nullification of nullable fields. All `null` values passed to `non-nullable` fields will be ignored.
  ///
  /// Usage
  /// ```dart
  /// FormFieldModel(...).copyWith(id: 12, name: "My name")
  /// ````
  FormFieldModel call({
    String name,
    String? label,
    String type,
    bool canEdit,
    dynamic options,
    String? value,
    String? placeholder,
    String? validation,
    String? dependency,
    Map<String, dynamic>? templateOptions,
    Map<String, dynamic>? calculation,
    FormData? formSection,
  });
}

/// Proxy class for `copyWith` functionality. This is a callable class and can be used as follows: `instanceOfFormFieldModel.copyWith(...)`.
class _$FormFieldModelCWProxyImpl implements _$FormFieldModelCWProxy {
  const _$FormFieldModelCWProxyImpl(this._value);

  final FormFieldModel _value;

  @override

  /// This function **does support** nullification of nullable fields. All `null` values passed to `non-nullable` fields will be ignored.
  ///
  /// Usage
  /// ```dart
  /// FormFieldModel(...).copyWith(id: 12, name: "My name")
  /// ````
  FormFieldModel call({
    Object? name = const $CopyWithPlaceholder(),
    Object? label = const $CopyWithPlaceholder(),
    Object? type = const $CopyWithPlaceholder(),
    Object? canEdit = const $CopyWithPlaceholder(),
    Object? options = const $CopyWithPlaceholder(),
    Object? value = const $CopyWithPlaceholder(),
    Object? placeholder = const $CopyWithPlaceholder(),
    Object? validation = const $CopyWithPlaceholder(),
    Object? dependency = const $CopyWithPlaceholder(),
    Object? templateOptions = const $CopyWithPlaceholder(),
    Object? calculation = const $CopyWithPlaceholder(),
    Object? formSection = const $CopyWithPlaceholder(),
  }) {
    return FormFieldModel(
      name: name == const $CopyWithPlaceholder()
          ? _value.name
          // ignore: cast_nullable_to_non_nullable
          : name as String,
      label: label == const $CopyWithPlaceholder()
          ? _value.label
          // ignore: cast_nullable_to_non_nullable
          : label as String?,
      type: type == const $CopyWithPlaceholder()
          ? _value.type
          // ignore: cast_nullable_to_non_nullable
          : type as String,
      canEdit: canEdit == const $CopyWithPlaceholder()
          ? _value.canEdit
          // ignore: cast_nullable_to_non_nullable
          : canEdit as bool,
      options: options == const $CopyWithPlaceholder()
          ? _value.options
          // ignore: cast_nullable_to_non_nullable
          : options as dynamic,
      value: value == const $CopyWithPlaceholder()
          ? _value.value
          // ignore: cast_nullable_to_non_nullable
          : value as String?,
      placeholder: placeholder == const $CopyWithPlaceholder()
          ? _value.placeholder
          // ignore: cast_nullable_to_non_nullable
          : placeholder as String?,
      validation: validation == const $CopyWithPlaceholder()
          ? _value.validation
          // ignore: cast_nullable_to_non_nullable
          : validation as String?,
      dependency: dependency == const $CopyWithPlaceholder()
          ? _value.dependency
          // ignore: cast_nullable_to_non_nullable
          : dependency as String?,
      templateOptions: templateOptions == const $CopyWithPlaceholder()
          ? _value.templateOptions
          // ignore: cast_nullable_to_non_nullable
          : templateOptions as Map<String, dynamic>?,
      calculation: calculation == const $CopyWithPlaceholder()
          ? _value.calculation
          // ignore: cast_nullable_to_non_nullable
          : calculation as Map<String, dynamic>?,
      formSection: formSection == const $CopyWithPlaceholder()
          ? _value.formSection
          // ignore: cast_nullable_to_non_nullable
          : formSection as FormData?,
    );
  }
}

extension $FormFieldModelCopyWith on FormFieldModel {
  /// Returns a callable class that can be used as follows: `instanceOfFormFieldModel.copyWith(...)`.
  // ignore: library_private_types_in_public_api
  _$FormFieldModelCWProxy get copyWith => _$FormFieldModelCWProxyImpl(this);

  /// Copies the object with the specific fields set to `null`. If you pass `false` as a parameter, nothing will be done and it will be ignored. Don't do it. Prefer `copyWith(field: null)`.
  ///
  /// Usage
  /// ```dart
  /// FormFieldModel(...).copyWithNull(firstField: true, secondField: true)
  /// ````
  FormFieldModel copyWithNull({
    bool label = false,
    bool value = false,
    bool placeholder = false,
    bool validation = false,
    bool dependency = false,
    bool templateOptions = false,
    bool calculation = false,
    bool formSection = false,
  }) {
    return FormFieldModel(
      name: name,
      label: label == true ? null : this.label,
      type: type,
      canEdit: canEdit,
      options: options,
      value: value == true ? null : this.value,
      placeholder: placeholder == true ? null : this.placeholder,
      validation: validation == true ? null : this.validation,
      dependency: dependency == true ? null : this.dependency,
      templateOptions: templateOptions == true ? null : this.templateOptions,
      calculation: calculation == true ? null : this.calculation,
      formSection: formSection == true ? null : this.formSection,
    );
  }
}
