import 'package:city_academy/core/util/enums.dart';
import 'package:city_academy/screens/form/models/form_section.dart';
import 'package:copy_with_extension/copy_with_extension.dart';

part 'form_data.g.dart';

@CopyWith()
class FormData {
  final String title;
  final String? description;
  final List<FormSection> sections;
  final DateTime? createdAt;
  final FormStatus? status;

  FormData({
    required this.title,
    this.description,
    required this.sections,
    this.createdAt,
    this.status,
  });

  factory FormData.fromJson(Map<String, dynamic> json) {
    final formData = json['response_data'] as Map<String, dynamic>? ?? json;
    return FormData(
      title: formData['title'] as String,
      description: formData['description'] as String?,
      sections: (formData['sections'] as List<dynamic>? ?? [])
          .map((section) =>
              FormSection.fromJson(section as Map<String, dynamic>))
          .toList(),
      createdAt: json['created_at'] != null
          ? DateTime.parse(json['created_at'] as String)
          : null,
      status: formData['status'] != null
          ? FormStatus.values.firstWhere((e) => e.name == formData['status'])
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> json = {
      'title': title,
      if (description != null) 'description': description,
      if (status != null) 'status': status!.name,
      'sections': sections.map((section) => section.toJson()).toList(),
      if (createdAt != null) 'created_at': createdAt!.toIso8601String(),
    };

    return json;
  }
}
