{"title": "Monitoramento HAS e DM", "status": "draft", "sections": [{"title": "MEDICAMENTOS", "description": "Por favor, assinale “SIM” ou “NÃO” às seguintes perguntas:", "requiredFields": ["medication_amlodipine", "medication_atenolol", "medication_atorvastatin", "medication_captopril", "medication_enalopril", "medication_glibenclamide", "medication_gliclazide", "medication_hydrochlorothiazide", "medication_insulin_glargine", "medication_insulin_nph", "medication_insulin_regular", "medication_losartan", "medication_metformin", "medication_olmes<PERSON>an", "medication_propranolol", "medication_levothyroxine", "medication_simvastatin", "medication_valsartan", "medication_not_reported"], "fields": [{"key": "medication_amlodipine", "label": "1. <PERSON><PERSON><PERSON><PERSON><PERSON> (bloqueador dos canais de cálcio)", "type": "boolean"}, {"key": "medication_atenolol", "label": "2. <PERSON><PERSON><PERSON><PERSON> (beta-bloqueador)", "type": "boolean"}, {"key": "medication_atorvastatin", "label": "3. <PERSON><PERSON><PERSON><PERSON><PERSON> (hipolipemiante)", "type": "boolean"}, {"key": "medication_captopril", "label": "4. <PERSON><PERSON><PERSON> (inibidor da ECA)", "type": "boolean"}, {"key": "medication_enalopril", "label": "5. <PERSON><PERSON><PERSON><PERSON> (inibidor da ECA)", "type": "boolean"}, {"key": "medication_glibenclamide", "label": "6. <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> (Antidiabético)", "type": "boolean"}, {"key": "medication_gliclazide", "label": "7. <PERSON><PERSON><PERSON><PERSON><PERSON>/<PERSON>ami<PERSON> (Antidiabético)", "type": "boolean"}, {"key": "medication_hydrochlorothiazide", "label": "8. <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> (diurético tiazídico)", "type": "boolean"}, {"key": "medication_insulin_glargine", "label": "9. <PERSON><PERSON><PERSON>/<PERSON><PERSON><PERSON> (longa duração)", "type": "boolean"}, {"key": "medication_insulin_nph", "label": "10. Insulina NPH (ação intermediária)", "type": "boolean"}, {"key": "medication_insulin_regular", "label": "11. <PERSON><PERSON><PERSON> regular (ação rápida)", "type": "boolean"}, {"key": "medication_losartan", "label": "12. <PERSON><PERSON><PERSON>/<PERSON> (bloqueador do receptor AT)", "type": "boolean"}, {"key": "medication_metformin", "label": "13. Met<PERSON><PERSON>/Glifage (Antidiabético)", "type": "boolean"}, {"key": "medication_olmes<PERSON>an", "label": "14. <PERSON><PERSON><PERSON><PERSON><PERSON>/Ben<PERSON> (bloqueador do receptor AT)", "type": "boolean"}, {"key": "medication_propranolol", "label": "15. <PERSON><PERSON><PERSON><PERSON><PERSON> (beta-bloqueador)", "type": "boolean"}, {"key": "medication_levothyroxine", "label": "16. <PERSON><PERSON><PERSON> T4/<PERSON><PERSON><PERSON><PERSON>a (Tratamento hipertireoidismo)", "type": "boolean"}, {"key": "medication_simvastatin", "label": "17. <PERSON><PERSON><PERSON><PERSON> (hipolipemiante)", "type": "boolean"}, {"key": "medication_valsartan", "label": "18. <PERSON><PERSON><PERSON>/<PERSON> (bloqueador do receptor AT)", "type": "boolean"}, {"key": "medication_not_reported", "label": "19. <PERSON><PERSON> relatado", "type": "boolean"}]}, {"title": "AFERIÇÃO", "description": "Registre a aferição diária", "requiredFields": ["systolic", "diastolic", "blood_glucose"], "fields": [{"key": "systolic", "label": "Sistólica (mmHg) *", "type": "textfield"}, {"key": "diastolic", "label": "Diastólica (mmHg) *", "type": "textfield"}, {"key": "blood_glucose", "label": "Glicemia (mg/dL) *", "type": "textfield"}]}]}