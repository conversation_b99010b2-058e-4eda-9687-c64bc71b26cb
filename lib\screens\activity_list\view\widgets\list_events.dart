import 'package:city_academy/core/util/enums.dart';
import 'package:city_academy/core/widgets/buttons/activity_card_button.dart';
import 'package:city_academy/screens/activity_list/model/activity_model.dart';
import 'package:flutter/material.dart';

class ListEvents extends StatelessWidget {
  final Future<void> Function() onRefresh;
  final List<ActivityModel> activityList;
  final ActivityType activityType;

  const ListEvents({
    super.key,
    required this.onRefresh,
    required this.activityList,
    required this.activityType,
  });

  @override
  Widget build(BuildContext context) {
    return RefreshIndicator(
      onRefresh: onRefresh,
      child: ListView(
        children: activityList.map((activity) {
          return Padding(
            padding: const EdgeInsets.symmetric(
              horizontal: 8.0,
              vertical: 10,
            ),
            child: ActivityCardButton(
              activity: activity,
              activityType: activityType,
            ),
          );
        }).toList(),
      ),
    );
  }
}
