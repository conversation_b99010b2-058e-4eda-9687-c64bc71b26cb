import 'package:city_academy/core/util/constants.dart';

class ClassificationRule {
  final double limit;
  final String label;

  ClassificationRule({required this.limit, required this.label});
}

class ClassificationRuleWithAge {
  final int maxAge;
  final Map<String, double> thresholds;

  ClassificationRuleWithAge({
    required this.maxAge,
    required this.thresholds,
  });
}

String getClassification(
  String fieldKey,
  double value, {
  String? gender,
  DateTime? birthDate,
}) {
  String mapKey = fieldKey;

  if (fieldKey == 'rce' && gender != null) {
    mapKey = gender == 'male' ? 'rce_men' : 'rce_women';
  }

  if (fieldKey == 'six_min_walk_test' && gender != null) {
    mapKey = gender == 'male' ? 'six_min_walk_men' : 'six_min_walk_women';
  }

  if (fieldKey == 'two_min_stationary_march_test' && gender != null) {
    mapKey = gender == 'male'
        ? 'two_min_stationary_march_men'
        : 'two_min_stationary_march_women';
  }

  if ((fieldKey == 'sit_and_reach' || fieldKey == 'push_ups') &&
      gender != null &&
      birthDate != null) {
    final today = DateTime.now();
    int age = today.year - birthDate.year;
    if (today.month < birthDate.month ||
        (today.month == birthDate.month && today.day < birthDate.day)) {
      age--;
    }

    if (gender == 'male') {
      if (age <= 30) {
        mapKey = '${fieldKey}_men_1_30';
      } else if (age <= 40) {
        mapKey = '${fieldKey}_men_40';
      } else {
        mapKey = '${fieldKey}_men_50';
      }
    } else {
      if (age <= 30) {
        mapKey = '${fieldKey}_women_1_30';
      } else if (age <= 40) {
        mapKey = '${fieldKey}_women_40';
      } else {
        mapKey = '${fieldKey}_women_50';
      }
    }
  }

  if (fieldKey == 'walk_test' && gender != null && birthDate != null) {
    final today = DateTime.now();
    int age = today.year - birthDate.year;
    if (today.month < birthDate.month ||
        (today.month == birthDate.month && today.day < birthDate.day)) {
      age--;
    }

    if (gender == 'male') {
      if (age <= 20) {
        mapKey = 'walk_test_male_20';
      } else if (age <= 30) {
        mapKey = 'walk_test_male_30';
      } else if (age <= 40) {
        mapKey = 'walk_test_male_40';
      } else if (age <= 50) {
        mapKey = 'walk_test_male_50';
      } else if (age <= 60) {
        mapKey = 'walk_test_male_60';
      } else {
        mapKey = 'walk_test_male_999';
      }
    } else {
      if (age <= 20) {
        mapKey = 'walk_test_female_20';
      } else if (age <= 30) {
        mapKey = 'walk_test_female_30';
      } else if (age <= 40) {
        mapKey = 'walk_test_female_40';
      } else if (age <= 50) {
        mapKey = 'walk_test_female_50';
      } else if (age <= 60) {
        mapKey = 'walk_test_female_60';
      } else {
        mapKey = 'walk_test_female_999';
      }
    }
  }

  final rules = classificationRules[mapKey];

  if (rules == null) return '';

  for (final rule in rules) {
    if (value < rule.limit) {
      return rule.label;
    }
  }

  return '';
}
