import 'package:city_academy/theme/colors.dart';
import 'package:city_academy/theme/texts.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';

class InfoRowButton extends StatelessWidget {
  final String? leadingIcon;
  final Color? leadingIconColor;
  final String title;
  final String? subTitle;
  final Function()? onTap;

  const InfoRowButton({
    super.key,
    this.leadingIcon,
    this.leadingIconColor,
    required this.title,
    this.subTitle,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: onTap,
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
        child: Row(
          children: [
            if (leadingIcon != null) ...[
              Container(
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(48),
                  color: leadingIconColor ?? CustomColors.greenDark,
                ),
                height: 45,
                width: 45,
                child: Center(
                  child: SvgPicture.asset(leadingIcon!, height: 25, width: 25),
                ),
              ),
              const SizedBox(width: 16),
            ],
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: titleText16.copyWith(
                      color: leadingIcon == null
                          ? CustomColors.neutralDark900
                          : CustomColors.neutralDark700,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  if (subTitle != null)
                    Text(
                      subTitle!,
                      style: titleText12.copyWith(
                        color: CustomColors.neutralDark700,
                        fontWeight: FontWeight.w300,
                      ),
                    ),
                ],
              ),
            ),
            const SizedBox(width: 16),
            const Icon(
              Icons.arrow_forward_ios,
              color: CustomColors.neutralDark700,
              size: 18,
            )
          ],
        ),
      ),
    );
  }
}
