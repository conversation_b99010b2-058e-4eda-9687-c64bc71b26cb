// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'meet_presence_state.dart';

// **************************************************************************
// CopyWithGenerator
// **************************************************************************

abstract class _$MeetPresenceStateCWProxy {
  /// This function **does support** nullification of nullable fields. All `null` values passed to `non-nullable` fields will be ignored.
  ///
  /// Usage
  /// ```dart
  /// MeetPresenceState(...).copyWith(id: 12, name: "My name")
  /// ````
  MeetPresenceState call({
    bool loading,
    String error,
    String message,
    List<String> listSelected,
    EventCityAdminModel eventCity,
    String activityId,
    List<Coach> coaches,
    EventCityAdminStatus status,
    int? meetingTopic,
  });
}

/// Proxy class for `copyWith` functionality. This is a callable class and can be used as follows: `instanceOfMeetPresenceState.copyWith(...)`.
class _$MeetPresenceStateCWProxyImpl implements _$MeetPresenceStateCWProxy {
  const _$MeetPresenceStateCWProxyImpl(this._value);

  final MeetPresenceState _value;

  @override

  /// This function **does support** nullification of nullable fields. All `null` values passed to `non-nullable` fields will be ignored.
  ///
  /// Usage
  /// ```dart
  /// MeetPresenceState(...).copyWith(id: 12, name: "My name")
  /// ````
  MeetPresenceState call({
    Object? loading = const $CopyWithPlaceholder(),
    Object? error = const $CopyWithPlaceholder(),
    Object? message = const $CopyWithPlaceholder(),
    Object? listSelected = const $CopyWithPlaceholder(),
    Object? eventCity = const $CopyWithPlaceholder(),
    Object? activityId = const $CopyWithPlaceholder(),
    Object? coaches = const $CopyWithPlaceholder(),
    Object? status = const $CopyWithPlaceholder(),
    Object? meetingTopic = const $CopyWithPlaceholder(),
  }) {
    return MeetPresenceState(
      loading: loading == const $CopyWithPlaceholder()
          ? _value.loading
          // ignore: cast_nullable_to_non_nullable
          : loading as bool,
      error: error == const $CopyWithPlaceholder()
          ? _value.error
          // ignore: cast_nullable_to_non_nullable
          : error as String,
      message: message == const $CopyWithPlaceholder()
          ? _value.message
          // ignore: cast_nullable_to_non_nullable
          : message as String,
      listSelected: listSelected == const $CopyWithPlaceholder()
          ? _value.listSelected
          // ignore: cast_nullable_to_non_nullable
          : listSelected as List<String>,
      eventCity: eventCity == const $CopyWithPlaceholder()
          ? _value.eventCity
          // ignore: cast_nullable_to_non_nullable
          : eventCity as EventCityAdminModel,
      activityId: activityId == const $CopyWithPlaceholder()
          ? _value.activityId
          // ignore: cast_nullable_to_non_nullable
          : activityId as String,
      coaches: coaches == const $CopyWithPlaceholder()
          ? _value.coaches
          // ignore: cast_nullable_to_non_nullable
          : coaches as List<Coach>,
      status: status == const $CopyWithPlaceholder()
          ? _value.status
          // ignore: cast_nullable_to_non_nullable
          : status as EventCityAdminStatus,
      meetingTopic: meetingTopic == const $CopyWithPlaceholder()
          ? _value.meetingTopic
          // ignore: cast_nullable_to_non_nullable
          : meetingTopic as int?,
    );
  }
}

extension $MeetPresenceStateCopyWith on MeetPresenceState {
  /// Returns a callable class that can be used as follows: `instanceOfMeetPresenceState.copyWith(...)`.
  // ignore: library_private_types_in_public_api
  _$MeetPresenceStateCWProxy get copyWith =>
      _$MeetPresenceStateCWProxyImpl(this);

  /// Copies the object with the specific fields set to `null`. If you pass `false` as a parameter, nothing will be done and it will be ignored. Don't do it. Prefer `copyWith(field: null)`.
  ///
  /// Usage
  /// ```dart
  /// MeetPresenceState(...).copyWithNull(firstField: true, secondField: true)
  /// ````
  MeetPresenceState copyWithNull({
    bool meetingTopic = false,
  }) {
    return MeetPresenceState(
      loading: loading,
      error: error,
      message: message,
      listSelected: listSelected,
      eventCity: eventCity,
      activityId: activityId,
      coaches: coaches,
      status: status,
      meetingTopic: meetingTopic == true ? null : this.meetingTopic,
    );
  }
}

// **************************************************************************
// EmptyGenerator
// **************************************************************************

class MeetPresenceStateEmpty extends MeetPresenceState {
  MeetPresenceStateEmpty()
      : super(
            listSelected: const [],
            eventCity: EventCityAdminModelEmpty(),
            activityId: '',
            coaches: const [],
            status: EventCityAdminStatus.values.first,
            meetingTopic: null,
            loading: false,
            error: '',
            message: '');
}
