import 'package:city_academy/core/util/enums.dart';
import 'package:city_academy/core/util/functions/funcs.dart';
import 'package:city_academy/core/util/global_instances.dart';
import 'package:city_academy/screens/event_city_admin/model/event_city_admin_model.dart';
import 'package:city_academy/screens/form/controller/form_service.dart';
import 'package:city_academy/screens/presence_form/controller/cubit/presence_form_state.dart';
import 'package:city_academy/screens/presence_form/controller/presence_form_service.dart';
import 'package:city_academy/screens/presence_form/model/presence_form_model.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class PresenceFormCubit extends Cubit<PresenceFormState> {
  PresenceFormCubit() : super(PresenceFormStateEmpty());

  Future<void> getCoaches() async {
    emit(state.copyWith(isLoading: true));
    try {
      final coaches = await FormService().fetchCoachs();
      emit(state.copyWith(coaches: coaches));
    } catch (e) {
      emit(state.copyWith(error: e.toString()));
    } finally {
      emit(state.copyWith(isLoading: false));
    }
  }

  Future<void> fetchActivity(String activityId) async {
    emit(state.copyWith(isLoading: true));
    try {
      final response = await PresenceFormService().getActivityDetails(
          loginCubit.state.user.activityCenterId!, activityId);

      emit(state.copyWith(eventCity: response));
    } catch (e) {
      emit(state.copyWith(error: e.toString()));
    } finally {
      emit(state.copyWith(isLoading: false));
    }
  }

  void onActivityTypeChanged(int? value) {
    final updatedForm = state.presenceFormModel.copyWith(activityType: value);
    emit(state.copyWith(presenceFormModel: updatedForm));
  }

  void onProfessionalChanged(String? value) {
    final updatedForm = state.presenceFormModel.copyWith(professional: value);
    emit(state.copyWith(presenceFormModel: updatedForm));
  }

  void onTargetAudienceChanged(int? value) {
    final updatedForm = state.presenceFormModel.copyWith(targetAudience: value);
    emit(state.copyWith(presenceFormModel: updatedForm));
  }

  void onHealthTopicChanged(int? value) {
    final updatedForm = state.presenceFormModel.copyWith(healthTopic: value);
    emit(state.copyWith(presenceFormModel: updatedForm));
  }

  void onHealthProcedureChanged(int? value) {
    final updatedForm =
        state.presenceFormModel.copyWith(healthProcedure: value);
    emit(state.copyWith(presenceFormModel: updatedForm));
  }

  bool isFormValid() {
    final form = state.presenceFormModel;
    return form.activityType != null &&
        form.professional != null &&
        form.targetAudience != null &&
        form.healthTopic != null &&
        form.healthProcedure != null;
  }

  void action() async {
    emit(
      state.copyWith(
        isLoading: true,
        status: EventCityAdminStatus.none,
      ),
    );
    try {
      final updatedEventCity = state.eventCity.copyWith(
        coachId: state.presenceFormModel.professional!,
        atividadeTipo: state.presenceFormModel.activityType!,
        temasParaSaude: [state.presenceFormModel.healthTopic!],
        praticasEmSaude: [state.presenceFormModel.healthProcedure!],
        publicoAlvo: [state.presenceFormModel.targetAudience!],
        turno: calculateTurn(state.eventCity.startTime),
      );

      await PresenceFormService().updateActivityDetails(updatedEventCity);

      emit(
        state.copyWith(
          isLoading: false,
          status: EventCityAdminStatus.succeeds,
        ),
      );
    } catch (e) {
      emit(
        state.copyWith(
          isLoading: false,
          error: e.toString(),
          status: EventCityAdminStatus.failed,
        ),
      );
    }
  }
}
