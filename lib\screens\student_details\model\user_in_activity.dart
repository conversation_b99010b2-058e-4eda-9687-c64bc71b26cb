import 'package:city_academy/core/util/enums.dart';
import 'package:city_academy/core/util/extentions.dart';
import 'package:city_academy/screens/login/model/user.dart';
import 'package:copy_with_extension/copy_with_extension.dart';
import 'package:empty_annotation/empty_annotation.dart';

part 'user_in_activity.g.dart';

@Empty()
@CopyWith()
class UserInActivity extends User {
  final String? emergencyContact;
  final String? relashionship;
  final bool? isComplete;
  final Map<String, bool>? diseases;
  final bool? hasConfirmation;

  UserInActivity({
    this.emergencyContact,
    this.relashionship,
    this.isComplete,
    this.diseases,
    this.hasConfirmation,
    required super.id,
    required super.sub,
    required super.type,
    required super.active,
    required super.createdAt,
    required super.updatedAt,
    required super.profileImageId,
    required super.firstName,
    required super.lastName,
    required super.nickname,
    required super.email,
    required super.phone,
    required super.gender,
    required super.genderIdentity,
    required super.race,
    required super.sexualOrientation,
    required super.birthDate,
    super.address,
    super.contacts,
    super.activityCenterId,
    super.document,
    super.cns,
    super.cref,
  });

  factory UserInActivity.fromJson(Map<String, dynamic> json) {
    final contact = json['contacts'] as List<dynamic>? ?? [];

    return UserInActivity(
      id: json['id'],
      sub: json['sub'],
      type: json['type'],
      active: json['active'],
      createdAt: DateTime.parse(json['created_at']),
      updatedAt: DateTime.parse(json['updated_at']),
      profileImageId: '',
      firstName: json['first_name'],
      lastName: json['last_name'],
      nickname: json['nickname'],
      email: json['email'],
      phone: json['phone'],
      gender: (json['gender'] as String? ?? 'male').toGender(),
      genderIdentity: json['gender_identity'],
      race: json['race'],
      document: json['document'],
      sexualOrientation: json['sexual_orientation'],
      birthDate: DateTime.parse(json['birth_date']),
      emergencyContact: contact.isNotEmpty ? contact[0]['value'] : null,
      relashionship: contact.isNotEmpty ? contact[0]['name'] : null,
      isComplete: json['has_completed_register'],
      diseases: json['diseases'] != null
          ? Map<String, bool>.from(json['diseases'])
          : null,
    );
  }
}
