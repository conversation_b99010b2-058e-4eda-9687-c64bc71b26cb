import 'package:city_academy/core/util/app_texts.dart';
import 'package:city_academy/core/util/global_instances.dart';
import 'package:city_academy/screens/form/models/form_data.dart';
import 'package:city_academy/screens/form/models/send_form_data_model.dart';
import 'package:city_academy/screens/login/model/coach.dart';

class FormRepository {
  Future<FormData> fetchForm(String formId) async {
    final response = await authClient.get(path: AppTexts.forms + formId);

    return FormData.fromJson(response.data['json_schema']);
  }

  Future<String> saveForm(SendFormDataModel model) async {
    final response = await authClient.post(
      path: AppTexts.formsResponse(model.formId),
      body: model.toSaveJson(),
    );

    return response.data['id'];
  }

  Future<bool> updateForm(SendFormDataModel model) async {
    final response = await authClient.patch(
      path: AppTexts.formsResponse(model.formId) + model.responseId!,
      body: model.toUpdateJson(),
    );

    return response.status == 200;
  }

  Future<FormData> fetchFormResponse(
    String formId,
    String responseId,
  ) async {
    final response =
        await authClient.get(path: AppTexts.formsResponse(formId) + responseId);

    return FormData.fromJson(response.data['response_data']);
  }

  Future<List<FormData>> fetchFormResponseList(
    String formId,
    String userId,
  ) async {
    final response = await authClient.get(
      path: AppTexts.formsResponse(formId),
      query: {'user_id': userId},
    );

    return (response.data['responses'] as List)
        .map<FormData>((e) => FormData.fromJson(e))
        .toList();
  }

  Future<List<Coach>> fetchCoachs() async {
    final response = await authClient.get(path: AppTexts.coach);

    return (response.data['items'] as List)
        .map<Coach>((e) => Coach.fromJson(e))
        .toList();
  }
}
