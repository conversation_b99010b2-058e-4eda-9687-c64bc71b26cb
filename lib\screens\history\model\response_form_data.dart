import 'package:city_academy/core/util/enums.dart';
import 'package:city_academy/screens/form/models/form_data.dart';
import 'package:city_academy/screens/form/models/form_section.dart';

class ResponseFormData extends FormData {
  final String responseId;

  ResponseFormData({
    required super.title,
    super.description,
    required super.sections,
    super.createdAt,
    super.status,
    required this.responseId,
  });

  factory ResponseFormData.fromJson(Map<String, dynamic> json) {
    final formData = json['response_data'] as Map<String, dynamic>? ?? json;
    return ResponseFormData(
      responseId: json['id'] as String,
      title: formData['title'] as String,
      description: formData['description'] as String?,
      sections: (formData['sections'] as List<dynamic>? ?? [])
          .map((section) =>
              FormSection.fromJson(section as Map<String, dynamic>))
          .toList(),
      createdAt: json['created_at'] != null
          ? DateTime.parse(json['created_at'] as String)
          : null,
      status: formData['status'] != null
          ? FormStatus.values.firstWhere((e) => e.name == formData['status'])
          : null,
    );
  }
}
