import 'package:city_academy/core/shared/base_service.dart';
import 'package:city_academy/screens/activity_list/controller/activity_list_repository.dart';
import 'package:city_academy/screens/activity_list/model/activity_model.dart';
import 'package:city_academy/screens/activity_list/model/activity_search_model.dart';

class ActivityListService extends BaseService {
  final _repository = ActivityListRepository();

  Future<List<ActivityModel>> fetchActivities(ActivitySearchModel model) {
    return super.call(() => _repository.fetchActivities(model));
  }
}
