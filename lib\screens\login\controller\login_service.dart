import 'package:city_academy/core/shared/base_service.dart';
import 'package:city_academy/screens/login/controller/login_repository.dart';
import 'package:city_academy/screens/login/model/user.dart';

class LoginService extends BaseService {
  final repository = LoginRepository();

  Future<User> loginUser(String email, String password) async {
    await repository.loginUser(email, password);
    return await repository.getCoachInfo();
  }

  Future<User> getCoachInfo() async {
    return await repository.getCoachInfo();
  }
}
