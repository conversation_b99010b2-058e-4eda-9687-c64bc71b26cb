import 'package:city_academy/core/util/enums.dart';
import 'package:city_academy/core/util/extentions.dart';
import 'package:city_academy/screens/student_details/model/user_in_activity.dart';
import 'package:city_academy/theme/colors.dart';
import 'package:city_academy/theme/texts.dart';
import 'package:flutter/material.dart';

class ProfileTile extends StatelessWidget {
  final UserInActivity user;

  const ProfileTile({super.key, required this.user});

  @override
  Widget build(BuildContext context) {
    return ListTile(
      leading: Container(
        decoration: const BoxDecoration(
          color: CustomColors.backgroundBeige,
          shape: BoxShape.circle,
        ),
        child: ClipRRect(
            borderRadius: const BorderRadius.all(Radius.circular(90)),
            child: Image.network(
              user.profileImageId,
              loadingBuilder: (context, child, loadingProgress) =>
                  const CircleAvatar(
                      backgroundColor: CustomColors.greenBase,
                      child: Icon(
                        Icons.person,
                        color: CustomColors.neutralWhite,
                        size: 30,
                      )),
              errorBuilder: (context, error, stackTrace) => const CircleAvatar(
                  backgroundColor: CustomColors.greenBase,
                  child: Icon(
                    Icons.person,
                    color: CustomColors.neutralWhite,
                    size: 30,
                  )),
            )),
      ),
      title: Text(
        user.name.nameImprovident(),
        style: titleText20.copyWith(color: CustomColors.neutralDark900),
      ),
      subtitle: Row(
        children: [
          Icon(
            user.gender == UserGender.male ? Icons.male : Icons.female,
            color: CustomColors.neutralDark900,
            size: 12,
          ),
          Text(
            user.gender.toStringRepresentation().nameImprovident(),
            style: titleText14.copyWith(
              color: CustomColors.neutralDark900,
              fontWeight: FontWeight.w400,
            ),
          ),
        ],
      ),
    );
  }
}
