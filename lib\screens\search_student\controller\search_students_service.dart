import 'package:city_academy/core/util/enums.dart';
import 'package:city_academy/screens/search_student/controller/search_students_repository.dart';
import 'package:city_academy/screens/student_details/model/user_in_activity.dart';
import 'package:dartz/dartz.dart';

class SearchStudentsService {
  SearchStudentsService();

  Future<Either<String, List<UserInActivity>>> call(
    SearchType type,
    String query,
  ) async {
    try {
      final students =
          await SearchStudentsRepository().searchStudents(type, query);
      return right(students);
    } catch (e) {
      return left(e.toString());
    }
  }
}
