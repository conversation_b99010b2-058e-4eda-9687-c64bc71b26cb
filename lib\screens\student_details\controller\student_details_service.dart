import 'package:city_academy/screens/student_details/controller/student_details_repository.dart';
import 'package:city_academy/screens/student_details/model/user_in_activity.dart';
import 'package:dartz/dartz.dart';

class StudentDetailsService {
  StudentDetailsService();

  Future<Either<String, UserInActivity>> call(String userId) async {
    try {
      final student =
          await StudentDetailsRepository().getStudentDetails(userId);
      return right(student);
    } catch (e) {
      return left(e.toString());
    }
  }
}
