import 'package:city_academy/screens/student_details/controller/student_details_repository.dart';
import 'package:city_academy/screens/student_details/model/user_in_activity.dart';
import 'package:dartz/dartz.dart';

class StudentDetailsService {
  StudentDetailsService();

  Future<Either<String, UserInActivity>> call(String document) async {
    try {
      final student =
          await StudentDetailsRepository().getStudentDetails(document);
      return right(student);
    } catch (e) {
      return left(e.toString());
    }
  }
}
