import 'package:city_academy/core/util/global_instances.dart';
import 'package:city_academy/routes/routes.dart';
import 'package:city_academy/screens/activity_list/view/activity_list_screen.dart';
import 'package:city_academy/screens/assessment_info/view/assessment_info_screen.dart';
import 'package:city_academy/screens/event_city_admin/model/event_city_admin_model.dart';
import 'package:city_academy/screens/event_city_admin/view/register_attendance_screen.dart';
import 'package:city_academy/screens/form/view/form_screen.dart';
import 'package:city_academy/screens/health_indicators/view/health_indicators_screen.dart';
import 'package:city_academy/screens/history/view/history_screen.dart';
import 'package:city_academy/screens/home/<USER>/home_screen.dart';
import 'package:city_academy/screens/login/view/login_screen.dart';
import 'package:city_academy/screens/meet/view/meet_screen.dart';
import 'package:city_academy/screens/meet_presence/view/meet_presence_screen.dart';
import 'package:city_academy/screens/monitoring_has_dm/view/monitoring_has_dm_screen.dart';
import 'package:city_academy/screens/presence_form/view/presence_form_screen.dart';
import 'package:city_academy/screens/register_info/view/register_info_screen.dart';
import 'package:city_academy/screens/search_student/view/search_student_screen.dart';
import 'package:city_academy/screens/splash/view/splash_screen.dart';
import 'package:city_academy/screens/student_details/view/student_details_screen.dart';
import 'package:go_router/go_router.dart';

GoRouter router = GoRouter(
  debugLogDiagnostics: true,
  initialLocation: Routes.splash,
  routes: [
    GoRoute(
      path: Routes.splash,
      builder: (context, state) => const SplashScreen(),
    ),
    GoRoute(
      path: Routes.login,
      builder: (context, state) => const LoginScreen(),
    ),
    GoRoute(
      path: Routes.home,
      builder: (context, state) => const HomeScreen(),
    ),
    GoRoute(
      path: Routes.studentSearch,
      builder: (context, state) {
        final extra = state.extra as List<dynamic>? ?? [];
        if (extra.isNotEmpty) {
          final isFromAttendanceRegistration = extra.first as bool? ?? false;
          final eventCity = extra.last as EventCityAdminModel?;
          return SearchStudentScreen(
            isFromAttendanceRegistration: isFromAttendanceRegistration,
            eventCity: eventCity,
          );
        }
        return const SearchStudentScreen();
      },
    ),
    GoRoute(
      path: Routes.studentDetails,
      builder: (context, state) => const StudentDetailsScreen(),
    ),
    GoRoute(
      path: Routes.activityList,
      builder: (context, state) => const ActivityListScreen.activities(),
    ),
    GoRoute(
      path: Routes.avaliationList,
      builder: (context, state) => const ActivityListScreen.avaliation(),
    ),
    GoRoute(
      path: Routes.registerInfo,
      builder: (context, state) => const RegisterInfoScreen(),
    ),
    GoRoute(
      path: Routes.assessmentsInfo,
      builder: (context, state) {
        final extra = state.extra as Map<String, dynamic>? ?? {};
        final readOnly = extra['readOnly'] as bool? ?? false;
        return AssessmentsInfoScreen(isReadOnly: readOnly);
      },
    ),
    GoRoute(
      path: Routes.historyAssessments,
      builder: (context, state) => const HistoryScreen.assessments(),
    ),
    GoRoute(
      path: Routes.historyMonitorment,
      builder: (context, state) => const HistoryScreen.monitorment(),
    ),
    GoRoute(
      path: Routes.registerAttendance,
      builder: (context, state) => const RegisterAttendanceScreen(),
    ),
    GoRoute(
      path: Routes.form,
      builder: (context, state) {
        final extra = state.extra as Map<String, dynamic>? ?? {};
        final readOnly = extra['readOnly'] as bool? ?? false;
        return DynamicFormScreen(isReadOnly: readOnly);
      },
    ),
    GoRoute(
      path: Routes.monitoring,
      builder: (context, state) {
        final extra = state.extra as Map<String, dynamic>? ?? {};
        final readOnly = extra['readOnly'] as bool? ?? false;
        return MonitoringHasDmScreen(isReadOnly: readOnly);
      },
    ),
    GoRoute(
      path: Routes.healthIndicators,
      builder: (context, state) => const HealthIndicatorsScreen(),
    ),
    GoRoute(
      path: Routes.meet,
      builder: (context, state) => const MeetScreen(),
    ),
    GoRoute(
      path: Routes.presenceForm,
      builder: (context, state) => const PresenceFormScreen(),
    ),
    GoRoute(
      path: Routes.meetPresence,
      builder: (context, state) => const MeetPresenceScreen(),
    ),
  ],
  redirect: (context, state) async {
    if (state.matchedLocation == Routes.splash ||
        state.matchedLocation == Routes.login) {
      return null;
    }

    final isAuthenticated = await authService.checkAuthentication();

    if (!isAuthenticated) {
      return Routes.login;
    }

    if (state.matchedLocation == Routes.login) {
      return Routes.home;
    }

    return null;
  },
);
