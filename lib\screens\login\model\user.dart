import 'package:city_academy/core/util/enums.dart';
import 'package:city_academy/core/util/extentions.dart';
import 'package:copy_with_extension/copy_with_extension.dart';
import 'package:empty_annotation/empty_annotation.dart';

part 'user.g.dart';

@Empty()
@CopyWith()
class User {
  final String id;
  final String sub;
  final String type;
  final bool active;
  final DateTime createdAt;
  final DateTime updatedAt;
  final String profileImageId;
  final String firstName;
  final String lastName;
  final String nickname;
  final String email;
  final String phone;
  final UserGender gender;
  final String genderIdentity;
  final String race;
  final String sexualOrientation;
  final DateTime birthDate;
  final Map<String, dynamic>? address;
  final List<dynamic>? contacts;
  final String? activityCenterId;
  final String? document;
  final String? cns;
  final String? cref;

  const User({
    required this.id,
    required this.sub,
    required this.type,
    required this.active,
    required this.createdAt,
    required this.updatedAt,
    required this.profileImageId,
    required this.firstName,
    required this.lastName,
    required this.nickname,
    required this.email,
    required this.phone,
    required this.gender,
    required this.genderIdentity,
    required this.race,
    required this.sexualOrientation,
    required this.birthDate,
    required this.address,
    required this.contacts,
    this.activityCenterId,
    this.document,
    this.cns,
    this.cref,
  });

  String get name => '$firstName $lastName';

  factory User.fromJson(Map<String, dynamic> json) => User(
        id: json['id'],
        sub: json['sub'],
        type: json['type'],
        active: json['active'],
        createdAt: DateTime.parse(json['created_at']),
        updatedAt: DateTime.parse(json['updated_at']),
        profileImageId: '',
        firstName: json['first_name'],
        lastName: json['last_name'],
        nickname: json['nickname'],
        email: json['email'],
        phone: json['phone'],
        gender: (json['gender'] as String? ?? 'male').toGender(),
        genderIdentity: json['gender_identity'],
        race: json['race'],
        sexualOrientation: json['sexual_orientation'],
        birthDate: DateTime.parse(json['birth_date']),
        address: json['address'],
        contacts: json['contacts'],
        activityCenterId: json['activity_center_id'],
        document: json['document'],
        cns: json['cns'],
        cref: json['cref'],
      );

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is User && runtimeType == other.runtimeType && id == other.id;

  @override
  int get hashCode => id.hashCode;
}
