import 'package:city_academy/core/util/constants.dart';
import 'package:city_academy/core/util/enums.dart';
import 'package:city_academy/screens/activity_list/controller/activity_list_service.dart';
import 'package:city_academy/screens/activity_list/controller/bloc/activity_list_state.dart';
import 'package:city_academy/screens/activity_list/model/activity_search_model.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class ActivityListCubit extends Cubit<ActivityListState> {
  ActivityListCubit() : super(ActivityListStateEmpty());

  void fetchActivities() async {
    emit(state.copyWith(isLoading: true));

    final initialDate = state.selectedDate ?? DateTime.now();
    final finalDate = endOfDay(initialDate);

    try {
      final model = ActivitySearchModel(
        centerId: state.centerId,
        initialDate: initialDate,
        finalDate: finalDate,
        activityType: state.activityType,
      );

      final activities = await ActivityListService().fetchActivities(model);

      emit(state.copyWith(
        activityList: activities,
        isLoading: false,
      ));
    } catch (_) {
      emit(state.copyWith(activityList: [], isLoading: false));
    }
  }

  void updateSelectedDate(DateTime value) {
    emit(state.copyWith(selectedDate: value));
    fetchActivities();
  }

  void updateCenterId(String value) {
    emit(state.copyWith(centerId: value));
    fetchActivities();
  }

  void updateActivityType(ActivityType value) {
    emit(state.copyWith(activityType: value));
    fetchActivities();
  }
}
