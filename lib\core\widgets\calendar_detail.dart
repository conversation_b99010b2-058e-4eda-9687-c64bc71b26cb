import 'package:city_academy/core/util/global_instances.dart';
import 'package:city_academy/core/widgets/activity_tile.dart';
import 'package:city_academy/core/widgets/buttons/custom_button.dart';
import 'package:city_academy/core/widgets/custom_dialog.dart';
import 'package:city_academy/routes/app_routes.dart';
import 'package:city_academy/screens/event_city_admin/model/event_city_admin_model.dart';
import 'package:city_academy/theme/colors.dart';
import 'package:city_academy/theme/texts.dart';
import 'package:flutter/material.dart';

class CalendarDetails extends StatelessWidget {
  final EventCityAdminModel eventCity;

  const CalendarDetails({super.key, required this.eventCity});

  @override
  Widget build(BuildContext context) {
    return Padding(
        padding: const EdgeInsets.only(
          top: 15.0,
        ),
        child: Column(
          children: [
            ActivityTile(eventCity: eventCity),
            const SizedBox(height: 12),
            TextButton(
              onPressed: () => showDialog(
                context: context,
                builder: (BuildContext context) {
                  return CustomDialog(
                    title: 'Cancelar',
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'Você realmente deseja cancelar essa aula?',
                          style: titleText14.copyWith(
                            color: CustomColors.neutralDark700,
                          ),
                        ),
                        const SizedBox(height: 30),
                        Row(
                          children: [
                            Expanded(
                              child: CustomButton(
                                onPressed: router.pop,
                                type: ButtonType.secondary,
                                title: 'Cancelar',
                              ),
                            ),
                            Expanded(
                              child: CustomButton(
                                  type: ButtonType.error,
                                  title: 'Sim',
                                  onPressed: () {
                                    eventCityAdminCubit
                                        .deleteActivity(eventCity);
                                    activityListCubit.fetchActivities();
                                    router.pop();
                                    router.pop();
                                  }),
                            ),
                          ],
                        ),
                      ],
                    ),
                  );
                },
              ),
              child: Container(
                margin: const EdgeInsets.symmetric(horizontal: 10),
                width: double.infinity,
                height: 35,
                decoration: BoxDecoration(
                  color: CustomColors.accentSoftRed.withValues(alpha: 0.4),
                  borderRadius: const BorderRadius.all(
                    Radius.circular(8),
                  ),
                ),
                child: Center(
                  child: Text(
                    'Cancelar aula',
                    style: titleText14.copyWith(
                      color: CustomColors.errorRed,
                    ),
                  ),
                ),
              ),
            ),
          ],
        ));
  }
}
