import 'package:city_academy/screens/login/controller/cubit/login_state.dart';
import 'package:city_academy/screens/login/controller/login_service.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class LoginCubit extends Cubit<LoginState> {
  LoginCubit() : super(LoginStateEmpty());

  void updatePassword(String value) => emit(state.copyWith(password: value));
  void updateEmail(String value) => emit(state.copyWith(email: value));

  Future<void> onLoginPressed() async {
    emit(state.copyWith(loading: true));
    try {
      final user = await LoginService().loginUser(state.email, state.password);
      emit(state.copyWith(user: user));
    } catch (e) {
      emit(state.copyWith(error: e.toString()));
    } finally {
      emit(state.copyWith(loading: false));
    }
  }

  Future<void> getCoachInfo() async {
    emit(state.copyWith(loading: true));
    try {
      final user = await LoginService().getCoachInfo();
      emit(state.copyWith(user: user));
    } catch (e) {
      emit(state.copyWith(error: e.toString()));
    } finally {
      emit(state.copyWith(loading: false));
    }
  }

  void reset() => emit(LoginStateEmpty());
}
