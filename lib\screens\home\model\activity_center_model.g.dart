// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'activity_center_model.dart';

// **************************************************************************
// CopyWithGenerator
// **************************************************************************

abstract class _$ActivityCenterModelCWProxy {
  /// This function **does support** nullification of nullable fields. All `null` values passed to `non-nullable` fields will be ignored.
  ///
  /// Usage
  /// ```dart
  /// ActivityCenterModel(...).copyWith(id: 12, name: "My name")
  /// ````
  ActivityCenterModel call({
    String id,
    String activityCenterName,
    String description,
    String address,
    int? district,
    String? cnes,
    String referencePoint,
    double latitude,
    double longitude,
    DateTime createdAt,
    DateTime updatedAt,
  });
}

/// Proxy class for `copyWith` functionality. This is a callable class and can be used as follows: `instanceOfActivityCenterModel.copyWith(...)`.
class _$ActivityCenterModelCWProxyImpl implements _$ActivityCenterModelCWProxy {
  const _$ActivityCenterModelCWProxyImpl(this._value);

  final ActivityCenterModel _value;

  @override

  /// This function **does support** nullification of nullable fields. All `null` values passed to `non-nullable` fields will be ignored.
  ///
  /// Usage
  /// ```dart
  /// ActivityCenterModel(...).copyWith(id: 12, name: "My name")
  /// ````
  ActivityCenterModel call({
    Object? id = const $CopyWithPlaceholder(),
    Object? activityCenterName = const $CopyWithPlaceholder(),
    Object? description = const $CopyWithPlaceholder(),
    Object? address = const $CopyWithPlaceholder(),
    Object? district = const $CopyWithPlaceholder(),
    Object? cnes = const $CopyWithPlaceholder(),
    Object? referencePoint = const $CopyWithPlaceholder(),
    Object? latitude = const $CopyWithPlaceholder(),
    Object? longitude = const $CopyWithPlaceholder(),
    Object? createdAt = const $CopyWithPlaceholder(),
    Object? updatedAt = const $CopyWithPlaceholder(),
  }) {
    return ActivityCenterModel(
      id: id == const $CopyWithPlaceholder()
          ? _value.id
          // ignore: cast_nullable_to_non_nullable
          : id as String,
      activityCenterName: activityCenterName == const $CopyWithPlaceholder()
          ? _value.activityCenterName
          // ignore: cast_nullable_to_non_nullable
          : activityCenterName as String,
      description: description == const $CopyWithPlaceholder()
          ? _value.description
          // ignore: cast_nullable_to_non_nullable
          : description as String,
      address: address == const $CopyWithPlaceholder()
          ? _value.address
          // ignore: cast_nullable_to_non_nullable
          : address as String,
      district: district == const $CopyWithPlaceholder()
          ? _value.district
          // ignore: cast_nullable_to_non_nullable
          : district as int?,
      cnes: cnes == const $CopyWithPlaceholder()
          ? _value.cnes
          // ignore: cast_nullable_to_non_nullable
          : cnes as String?,
      referencePoint: referencePoint == const $CopyWithPlaceholder()
          ? _value.referencePoint
          // ignore: cast_nullable_to_non_nullable
          : referencePoint as String,
      latitude: latitude == const $CopyWithPlaceholder()
          ? _value.latitude
          // ignore: cast_nullable_to_non_nullable
          : latitude as double,
      longitude: longitude == const $CopyWithPlaceholder()
          ? _value.longitude
          // ignore: cast_nullable_to_non_nullable
          : longitude as double,
      createdAt: createdAt == const $CopyWithPlaceholder()
          ? _value.createdAt
          // ignore: cast_nullable_to_non_nullable
          : createdAt as DateTime,
      updatedAt: updatedAt == const $CopyWithPlaceholder()
          ? _value.updatedAt
          // ignore: cast_nullable_to_non_nullable
          : updatedAt as DateTime,
    );
  }
}

extension $ActivityCenterModelCopyWith on ActivityCenterModel {
  /// Returns a callable class that can be used as follows: `instanceOfActivityCenterModel.copyWith(...)`.
  // ignore: library_private_types_in_public_api
  _$ActivityCenterModelCWProxy get copyWith =>
      _$ActivityCenterModelCWProxyImpl(this);

  /// Copies the object with the specific fields set to `null`. If you pass `false` as a parameter, nothing will be done and it will be ignored. Don't do it. Prefer `copyWith(field: null)`.
  ///
  /// Usage
  /// ```dart
  /// ActivityCenterModel(...).copyWithNull(firstField: true, secondField: true)
  /// ````
  ActivityCenterModel copyWithNull({
    bool district = false,
    bool cnes = false,
  }) {
    return ActivityCenterModel(
      id: id,
      activityCenterName: activityCenterName,
      description: description,
      address: address,
      district: district == true ? null : this.district,
      cnes: cnes == true ? null : this.cnes,
      referencePoint: referencePoint,
      latitude: latitude,
      longitude: longitude,
      createdAt: createdAt,
      updatedAt: updatedAt,
    );
  }
}

// **************************************************************************
// EmptyGenerator
// **************************************************************************

class ActivityCenterModelEmpty extends ActivityCenterModel {
  ActivityCenterModelEmpty()
      : super(
            id: '',
            activityCenterName: '',
            description: '',
            address: '',
            district: null,
            cnes: null,
            referencePoint: '',
            latitude: 0.0,
            longitude: 0.0,
            createdAt: DateTime.now(),
            updatedAt: DateTime.now());
}
