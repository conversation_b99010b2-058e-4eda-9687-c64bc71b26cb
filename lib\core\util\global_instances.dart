import 'package:city_academy/core/api/api_client.dart';
import 'package:city_academy/core/api/auth_service.dart';
import 'package:city_academy/core/storage/storage.dart';
import 'package:city_academy/core/util/app_texts.dart';
import 'package:city_academy/injection.dart';
import 'package:city_academy/screens/activity_list/controller/bloc/activity_list_cubit.dart';
import 'package:city_academy/screens/event_city_admin/controller/bloc/event_city_admin_cubit.dart';
import 'package:city_academy/screens/form/controller/cubit/form_cubit.dart';
import 'package:city_academy/screens/health_indicators/controller/cubit/health_indicators_cubit.dart';
import 'package:city_academy/screens/history/controller/cubit/history_cubit.dart';
import 'package:city_academy/screens/home/<USER>/cubit/home_cubit.dart';
import 'package:city_academy/screens/login/controller/cubit/login_cubit.dart';
import 'package:city_academy/screens/meet_presence/controller/bloc/meet_presence_cubit.dart';
import 'package:city_academy/screens/presence_form/controller/cubit/presence_form_cubit.dart';
import 'package:city_academy/screens/search_student/controller/bloc/search_students_bloc.dart';
import 'package:city_academy/screens/student_details/controller/bloc/student_details_bloc.dart';

// Globals

ApiClient authClient = getIt<ApiClient>(instanceName: AppTexts.authClient);
ApiClient unAuthClient = getIt<ApiClient>(instanceName: AppTexts.unAuthClient);
final storage = getIt<Storage>();
final authService = getIt<AuthService>();

// Cubits
final activityListCubit = getIt<ActivityListCubit>();
final formCubit = getIt<FormCubit>();
final studentDetailsBloc = getIt<StudentDetailsBloc>();
final eventCityAdminCubit = getIt<EventCityAdminCubit>();
final loginCubit = getIt<LoginCubit>();
final searchStudentsBloc = getIt<SearchStudentsBloc>();
final historyCubit = getIt<HistoryCubit>();
final healthIndicatorsCubit = getIt<HealthIndicatorsCubit>();
final homeCubit = getIt<HomeCubit>();
final presenceFormCubit = getIt<PresenceFormCubit>();
final meetPresenceCubit = getIt<MeetPresenceCubit>();

// Services
