import 'package:city_academy/core/util/global_instances.dart';
import 'package:city_academy/core/widgets/buttons/info_row_button.dart';
import 'package:city_academy/routes/app_routes.dart';
import 'package:city_academy/routes/routes.dart';
import 'package:city_academy/screens/form/models/form_field_model.dart';
import 'package:flutter/material.dart';

Widget buildRoute(
  BuildContext context,
  FormFieldModel field,
  Map<String, dynamic> extraData,
) {
  return Padding(
    padding: const EdgeInsets.only(bottom: 16.0),
    child: InfoRowButton(
      title: field.label ?? '',
      onTap: () {
        router.push(Routes.form, extra: {
          'title': field.calculation?["form"]["title"],
          'description': field.calculation?["form"]["description"],
          'sections': [field.calculation?["form"]],
        }).then((_) => formCubit.setFormData(extraData));
      },
    ),
  );
}
