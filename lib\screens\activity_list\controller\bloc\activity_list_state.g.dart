// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'activity_list_state.dart';

// **************************************************************************
// CopyWithGenerator
// **************************************************************************

abstract class _$ActivityListStateCWProxy {
  /// This function **does support** nullification of nullable fields. All `null` values passed to `non-nullable` fields will be ignored.
  ///
  /// Usage
  /// ```dart
  /// ActivityListState(...).copyWith(id: 12, name: "My name")
  /// ````
  ActivityListState call({
    DateTime? selectedDate,
    String centerId,
    bool isLoading,
    List<ActivityModel> activityList,
    ActivityType activityType,
  });
}

/// Proxy class for `copyWith` functionality. This is a callable class and can be used as follows: `instanceOfActivityListState.copyWith(...)`.
class _$ActivityListStateCWProxyImpl implements _$ActivityListStateCWProxy {
  const _$ActivityListStateCWProxyImpl(this._value);

  final ActivityListState _value;

  @override

  /// This function **does support** nullification of nullable fields. All `null` values passed to `non-nullable` fields will be ignored.
  ///
  /// Usage
  /// ```dart
  /// ActivityListState(...).copyWith(id: 12, name: "My name")
  /// ````
  ActivityListState call({
    Object? selectedDate = const $CopyWithPlaceholder(),
    Object? centerId = const $CopyWithPlaceholder(),
    Object? isLoading = const $CopyWithPlaceholder(),
    Object? activityList = const $CopyWithPlaceholder(),
    Object? activityType = const $CopyWithPlaceholder(),
  }) {
    return ActivityListState(
      selectedDate: selectedDate == const $CopyWithPlaceholder()
          ? _value.selectedDate
          // ignore: cast_nullable_to_non_nullable
          : selectedDate as DateTime?,
      centerId: centerId == const $CopyWithPlaceholder()
          ? _value.centerId
          // ignore: cast_nullable_to_non_nullable
          : centerId as String,
      isLoading: isLoading == const $CopyWithPlaceholder()
          ? _value.isLoading
          // ignore: cast_nullable_to_non_nullable
          : isLoading as bool,
      activityList: activityList == const $CopyWithPlaceholder()
          ? _value.activityList
          // ignore: cast_nullable_to_non_nullable
          : activityList as List<ActivityModel>,
      activityType: activityType == const $CopyWithPlaceholder()
          ? _value.activityType
          // ignore: cast_nullable_to_non_nullable
          : activityType as ActivityType,
    );
  }
}

extension $ActivityListStateCopyWith on ActivityListState {
  /// Returns a callable class that can be used as follows: `instanceOfActivityListState.copyWith(...)`.
  // ignore: library_private_types_in_public_api
  _$ActivityListStateCWProxy get copyWith =>
      _$ActivityListStateCWProxyImpl(this);

  /// Copies the object with the specific fields set to `null`. If you pass `false` as a parameter, nothing will be done and it will be ignored. Don't do it. Prefer `copyWith(field: null)`.
  ///
  /// Usage
  /// ```dart
  /// ActivityListState(...).copyWithNull(firstField: true, secondField: true)
  /// ````
  ActivityListState copyWithNull({
    bool selectedDate = false,
  }) {
    return ActivityListState(
      selectedDate: selectedDate == true ? null : this.selectedDate,
      centerId: centerId,
      isLoading: isLoading,
      activityList: activityList,
      activityType: activityType,
    );
  }
}

// **************************************************************************
// EmptyGenerator
// **************************************************************************

class ActivityListStateEmpty extends ActivityListState {
  ActivityListStateEmpty()
      : super(
            isLoading: false,
            activityList: const [],
            centerId: '',
            selectedDate: null,
            activityType: ActivityType.values.first);
}
