import 'package:copy_with_extension/copy_with_extension.dart';
import 'package:empty_annotation/empty_annotation.dart';

part 'activity_model.g.dart';

@Empty()
@CopyWith()
class ActivityModel {
  String activityId;
  String activityCenterId;
  DateTime startTime;
  DateTime endTime;
  String description;
  String eventName;

  ActivityModel({
    required this.activityId,
    required this.activityCenterId,
    required this.startTime,
    required this.endTime,
    required this.description,
    required this.eventName,
  });

  factory ActivityModel.fromJson(Map<String, dynamic> json) {
    return ActivityModel(
      activityId: json['id'],
      activityCenterId: json['activity_center_id'],
      startTime: DateTime.parse(json['start_time']),
      endTime: DateTime.parse(json['end_time']),
      description: json['description'] ?? '',
      eventName: json['event_name'] ?? '',
    );
  }
}
