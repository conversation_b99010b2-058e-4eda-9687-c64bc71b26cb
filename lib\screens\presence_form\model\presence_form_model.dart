import 'package:copy_with_extension/copy_with_extension.dart';
import 'package:empty_annotation/empty_annotation.dart';

part 'presence_form_model.g.dart';

@CopyWith()
@Empty()
class PresenceFormModel {
  final int? activityType;
  final String? professional;
  final int? targetAudience;
  final int? healthTopic;
  final int? healthProcedure;

  PresenceFormModel({
    this.activityType,
    this.professional,
    this.targetAudience,
    this.healthTopic,
    this.healthProcedure,
  });
}
