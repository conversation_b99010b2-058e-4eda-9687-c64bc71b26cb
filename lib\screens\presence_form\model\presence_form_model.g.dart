// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'presence_form_model.dart';

// **************************************************************************
// CopyWithGenerator
// **************************************************************************

abstract class _$PresenceFormModelCWProxy {
  /// This function **does support** nullification of nullable fields. All `null` values passed to `non-nullable` fields will be ignored.
  ///
  /// Usage
  /// ```dart
  /// PresenceFormModel(...).copyWith(id: 12, name: "My name")
  /// ````
  PresenceFormModel call({
    int? activityType,
    String? professional,
    int? targetAudience,
    int? healthTopic,
    int? healthProcedure,
  });
}

/// Proxy class for `copyWith` functionality. This is a callable class and can be used as follows: `instanceOfPresenceFormModel.copyWith(...)`.
class _$PresenceFormModelCWProxyImpl implements _$PresenceFormModelCWProxy {
  const _$PresenceFormModelCWProxyImpl(this._value);

  final PresenceFormModel _value;

  @override

  /// This function **does support** nullification of nullable fields. All `null` values passed to `non-nullable` fields will be ignored.
  ///
  /// Usage
  /// ```dart
  /// PresenceFormModel(...).copyWith(id: 12, name: "My name")
  /// ````
  PresenceFormModel call({
    Object? activityType = const $CopyWithPlaceholder(),
    Object? professional = const $CopyWithPlaceholder(),
    Object? targetAudience = const $CopyWithPlaceholder(),
    Object? healthTopic = const $CopyWithPlaceholder(),
    Object? healthProcedure = const $CopyWithPlaceholder(),
  }) {
    return PresenceFormModel(
      activityType: activityType == const $CopyWithPlaceholder()
          ? _value.activityType
          // ignore: cast_nullable_to_non_nullable
          : activityType as int?,
      professional: professional == const $CopyWithPlaceholder()
          ? _value.professional
          // ignore: cast_nullable_to_non_nullable
          : professional as String?,
      targetAudience: targetAudience == const $CopyWithPlaceholder()
          ? _value.targetAudience
          // ignore: cast_nullable_to_non_nullable
          : targetAudience as int?,
      healthTopic: healthTopic == const $CopyWithPlaceholder()
          ? _value.healthTopic
          // ignore: cast_nullable_to_non_nullable
          : healthTopic as int?,
      healthProcedure: healthProcedure == const $CopyWithPlaceholder()
          ? _value.healthProcedure
          // ignore: cast_nullable_to_non_nullable
          : healthProcedure as int?,
    );
  }
}

extension $PresenceFormModelCopyWith on PresenceFormModel {
  /// Returns a callable class that can be used as follows: `instanceOfPresenceFormModel.copyWith(...)`.
  // ignore: library_private_types_in_public_api
  _$PresenceFormModelCWProxy get copyWith =>
      _$PresenceFormModelCWProxyImpl(this);

  /// Copies the object with the specific fields set to `null`. If you pass `false` as a parameter, nothing will be done and it will be ignored. Don't do it. Prefer `copyWith(field: null)`.
  ///
  /// Usage
  /// ```dart
  /// PresenceFormModel(...).copyWithNull(firstField: true, secondField: true)
  /// ````
  PresenceFormModel copyWithNull({
    bool activityType = false,
    bool professional = false,
    bool targetAudience = false,
    bool healthTopic = false,
    bool healthProcedure = false,
  }) {
    return PresenceFormModel(
      activityType: activityType == true ? null : this.activityType,
      professional: professional == true ? null : this.professional,
      targetAudience: targetAudience == true ? null : this.targetAudience,
      healthTopic: healthTopic == true ? null : this.healthTopic,
      healthProcedure: healthProcedure == true ? null : this.healthProcedure,
    );
  }
}

// **************************************************************************
// EmptyGenerator
// **************************************************************************

class PresenceFormModelEmpty extends PresenceFormModel {
  PresenceFormModelEmpty()
      : super(
            activityType: null,
            professional: null,
            targetAudience: null,
            healthTopic: null,
            healthProcedure: null);
}
