import 'package:city_academy/core/shared/base_service.dart';
import 'package:city_academy/screens/home/<USER>/home_repository.dart';
import 'package:city_academy/screens/home/<USER>/activity_center_model.dart';

class HomeService extends BaseService {
  final repository = HomeRepository();

  Future<List<ActivityCenterModel>> fetchActivityCenters() {
    return super.call(repository.fetchActivityCenters);
  }
}
