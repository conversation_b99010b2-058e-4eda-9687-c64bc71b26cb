import 'package:city_academy/screens/form/models/form_data.dart';
import 'package:copy_with_extension/copy_with_extension.dart';

part 'send_form_data_model.g.dart';

@CopyWith()
class SendFormDataModel {
  final String formId;
  final FormData formData;
  final String? traineeId;
  final String? responseId;

  SendFormDataModel({
    required this.formId,
    required this.formData,
    this.traineeId,
    this.responseId,
  });

  Map<String, dynamic> toSaveJson() {
    return {
      'response_data': formData.toJson(),
      'trainee_id': traineeId,
    };
  }

  Map<String, dynamic> toUpdateJson() {
    return {
      'response_data': formData.toJson(),
    };
  }
}
