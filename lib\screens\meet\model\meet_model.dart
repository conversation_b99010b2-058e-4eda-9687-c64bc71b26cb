import 'package:copy_with_extension/copy_with_extension.dart';
import 'package:empty_annotation/empty_annotation.dart';

part 'meet_model.g.dart';

@CopyWith()
@Empty()
class MeetModel {
  final String name;
  final String description;
  final DateTime startTime;
  final DateTime endTime;
  final String activityCenterId;
  final String coachId;

  MeetModel({
    required this.name,
    required this.description,
    required this.startTime,
    required this.endTime,
    required this.activityCenterId,
    required this.coachId,
  });

  Map<String, dynamic> toJson() {
    return {
      "type": "meeting",
      "activity_center_id": activityCenterId,
      'start_time': startTime.toIso8601String(),
      'end_time': endTime.toIso8601String(),
      'activity_time': startTime.toIso8601String(),
      "max_qtd": 1,
      "min_qtd": 1,
      "active": true,
      "status": "active",
      'description': description,
      'event_name': name,
      'coach_id': coachId,
    };
  }
}
