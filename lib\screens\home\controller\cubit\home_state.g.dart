// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'home_state.dart';

// **************************************************************************
// CopyWithGenerator
// **************************************************************************

abstract class _$HomeStateCWProxy {
  /// This function **does support** nullification of nullable fields. All `null` values passed to `non-nullable` fields will be ignored.
  ///
  /// Usage
  /// ```dart
  /// HomeState(...).copyWith(id: 12, name: "My name")
  /// ````
  HomeState call({
    List<ActivityCenterModel> activityCenterList,
    ActivityCenterModel? selectedActivityCenter,
    bool loading,
    String error,
    String message,
  });
}

/// Proxy class for `copyWith` functionality. This is a callable class and can be used as follows: `instanceOfHomeState.copyWith(...)`.
class _$HomeStateCWProxyImpl implements _$HomeStateCWProxy {
  const _$HomeStateCWProxyImpl(this._value);

  final HomeState _value;

  @override

  /// This function **does support** nullification of nullable fields. All `null` values passed to `non-nullable` fields will be ignored.
  ///
  /// Usage
  /// ```dart
  /// HomeState(...).copyWith(id: 12, name: "My name")
  /// ````
  HomeState call({
    Object? activityCenterList = const $CopyWithPlaceholder(),
    Object? selectedActivityCenter = const $CopyWithPlaceholder(),
    Object? loading = const $CopyWithPlaceholder(),
    Object? error = const $CopyWithPlaceholder(),
    Object? message = const $CopyWithPlaceholder(),
  }) {
    return HomeState(
      activityCenterList: activityCenterList == const $CopyWithPlaceholder()
          ? _value.activityCenterList
          // ignore: cast_nullable_to_non_nullable
          : activityCenterList as List<ActivityCenterModel>,
      selectedActivityCenter:
          selectedActivityCenter == const $CopyWithPlaceholder()
              ? _value.selectedActivityCenter
              // ignore: cast_nullable_to_non_nullable
              : selectedActivityCenter as ActivityCenterModel?,
      loading: loading == const $CopyWithPlaceholder()
          ? _value.loading
          // ignore: cast_nullable_to_non_nullable
          : loading as bool,
      error: error == const $CopyWithPlaceholder()
          ? _value.error
          // ignore: cast_nullable_to_non_nullable
          : error as String,
      message: message == const $CopyWithPlaceholder()
          ? _value.message
          // ignore: cast_nullable_to_non_nullable
          : message as String,
    );
  }
}

extension $HomeStateCopyWith on HomeState {
  /// Returns a callable class that can be used as follows: `instanceOfHomeState.copyWith(...)`.
  // ignore: library_private_types_in_public_api
  _$HomeStateCWProxy get copyWith => _$HomeStateCWProxyImpl(this);

  /// Copies the object with the specific fields set to `null`. If you pass `false` as a parameter, nothing will be done and it will be ignored. Don't do it. Prefer `copyWith(field: null)`.
  ///
  /// Usage
  /// ```dart
  /// HomeState(...).copyWithNull(firstField: true, secondField: true)
  /// ````
  HomeState copyWithNull({
    bool selectedActivityCenter = false,
  }) {
    return HomeState(
      activityCenterList: activityCenterList,
      selectedActivityCenter:
          selectedActivityCenter == true ? null : this.selectedActivityCenter,
      loading: loading,
      error: error,
      message: message,
    );
  }
}

// **************************************************************************
// EmptyGenerator
// **************************************************************************

class HomeStateEmpty extends HomeState {
  HomeStateEmpty()
      : super(
            activityCenterList: const [],
            selectedActivityCenter: null,
            loading: false,
            error: '',
            message: '');
}
