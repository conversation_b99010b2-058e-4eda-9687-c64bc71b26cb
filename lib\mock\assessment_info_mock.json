{"title": "Informações da avaliação", "status": "draft", "sections": [{"title": "OBJETIVO(S)", "description": "Por favor, assinale “SIM” ou “NÃO” às seguintes perguntas:", "requiredFields": ["objective"], "fields": [{"key": "objective", "type": "select", "templateOptions": {"label": "", "options": [{"value": "goal_lose_weight", "label": "Emagrecer"}, {"value": "goal_promote_health", "label": "Promover a saúde"}, {"value": "goal_follow_medical_recommendations", "label": "Atender à recomendações médica"}, {"value": "goal_improve_physical_fitness", "label": "Melhorar o condicionamento físico"}, {"value": "goal_help_control_dcn", "label": "Auxiliar no controle de DCNT"}, {"value": "goal_socialize", "label": "Socializar"}, {"value": "goal_improve_mental_health", "label": "<PERSON><PERSON><PERSON> a saúde mental"}, {"value": "goal_not_reported", "label": "Não relatado"}]}}]}, {"title": "SINAIS E SINTOMAS", "description": "Por favor, assinale “SIM” ou “NÃO” às seguintes perguntas:", "requiredFields": ["symptom_chest_pain", "symptom_irregular_heartbeat", "symptom_breathing_difficulty", "symptom_unexplained_weight_loss", "symptom_infection_with_fever", "symptom_fever_with_dehydration", "symptom_leg_pain", "symptom_hernia_discomfort", "symptom_foot_ankle_wounds", "symptom_swollen_joints", "symptom_fall_difficulties", "symptom_eyes_problems", "symptom_not_present"], "fields": [{"key": "symptom_chest_pain", "label": "1. <PERSON><PERSON> no peito", "type": "boolean"}, {"key": "symptom_irregular_heartbeat", "label": "2. Batimentos cardíacos irregulares e rápidos ou palpitações", "type": "boolean"}, {"key": "symptom_breathing_difficulty", "label": "3. <PERSON><PERSON><PERSON><PERSON> para respirar", "type": "boolean"}, {"key": "symptom_unexplained_weight_loss", "label": "4. Grande perda de peso sem motivo conhecido", "type": "boolean"}, {"key": "symptom_infection_with_fever", "label": "5. Infecção acompanhada de febre", "type": "boolean"}, {"key": "symptom_fever_with_dehydration", "label": "6. Febre com desidratação e o coração acelerado", "type": "boolean"}, {"key": "symptom_leg_pain", "label": "7. <PERSON><PERSON> <PERSON> perna", "type": "boolean"}, {"key": "symptom_hernia_discomfort", "label": "8. <PERSON><PERSON><PERSON><PERSON> abdominal incomodando", "type": "boolean"}, {"key": "symptom_foot_ankle_wounds", "label": "9. Feridas nos pés ou tornozelos que não saram", "type": "boolean"}, {"key": "symptom_swollen_joints", "label": "10. <PERSON><PERSON> inchadas", "type": "boolean"}, {"key": "symptom_fall_difficulties", "label": "11. <PERSON><PERSON> de causou dificuldades para caminhar e/ou dor persistentes", "type": "boolean"}, {"key": "symptom_eyes_problems", "label": "13. Problema nos o<PERSON>hos", "type": "boolean"}, {"key": "symptom_not_present", "label": "14. <PERSON><PERSON>", "type": "boolean"}]}, {"title": "HISTÓRICO FAMILIAR", "description": "Por favor, assinale “SIM” ou “NÃO” às seguintes perguntas:", "requiredFields": ["family_heart_disease", "family_hypertension", "family_diabetes", "family_dyslipidemia", "family_not_reported"], "fields": [{"key": "family_heart_disease", "label": "1. Cardiopatia", "type": "boolean"}, {"key": "family_hypertension", "label": "2. Hipertensão arterial", "type": "boolean"}, {"key": "family_diabetes", "label": "3. Di<PERSON><PERSON>", "type": "boolean"}, {"key": "family_dyslipidemia", "label": "4. <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "boolean"}, {"key": "family_not_reported", "label": "5. <PERSON><PERSON> relatado", "type": "boolean"}]}, {"title": "MEDICAMENTOS", "description": "Por favor, assinale “SIM” ou “NÃO” às seguintes perguntas:", "requiredFields": ["medication_amlodipine", "medication_atenolol", "medication_atorvastatin", "medication_captopril", "medication_enalopril", "medication_glibenclamide", "medication_gliclazide", "medication_hydrochlorothiazide", "medication_insulin_glargine", "medication_insulin_nph", "medication_insulin_regular", "medication_losartan", "medication_metformin", "medication_olmes<PERSON>an", "medication_propranolol", "medication_levothyroxine", "medication_simvastatin", "medication_valsartan", "medication_not_reported"], "fields": [{"key": "medication_amlodipine", "label": "1. <PERSON><PERSON><PERSON><PERSON><PERSON> (bloqueador dos canais de cálcio)", "type": "boolean"}, {"key": "medication_atenolol", "label": "2. <PERSON><PERSON><PERSON><PERSON> (beta-bloqueador)", "type": "boolean"}, {"key": "medication_atorvastatin", "label": "3. <PERSON><PERSON><PERSON><PERSON><PERSON> (hipolipemiante)", "type": "boolean"}, {"key": "medication_captopril", "label": "4. <PERSON><PERSON><PERSON> (inibidor da ECA)", "type": "boolean"}, {"key": "medication_enalopril", "label": "5. <PERSON><PERSON><PERSON><PERSON> (inibidor da ECA)", "type": "boolean"}, {"key": "medication_glibenclamide", "label": "6. <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> (Antidiabético)", "type": "boolean"}, {"key": "medication_gliclazide", "label": "7. <PERSON><PERSON><PERSON><PERSON><PERSON>/<PERSON>ami<PERSON> (Antidiabético)", "type": "boolean"}, {"key": "medication_hydrochlorothiazide", "label": "8. <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> (diurético tiazídico)", "type": "boolean"}, {"key": "medication_insulin_glargine", "label": "9. <PERSON><PERSON><PERSON>/<PERSON><PERSON><PERSON> (longa duração)", "type": "boolean"}, {"key": "medication_insulin_nph", "label": "10. Insulina NPH (ação intermediária)", "type": "boolean"}, {"key": "medication_insulin_regular", "label": "11. <PERSON><PERSON><PERSON> regular (ação rápida)", "type": "boolean"}, {"key": "medication_losartan", "label": "12. <PERSON><PERSON><PERSON>/<PERSON> (bloqueador do receptor AT)", "type": "boolean"}, {"key": "medication_metformin", "label": "13. Met<PERSON><PERSON>/Glifage (Antidiabético)", "type": "boolean"}, {"key": "medication_olmes<PERSON>an", "label": "14. <PERSON><PERSON><PERSON><PERSON><PERSON>/Ben<PERSON> (bloqueador do receptor AT)", "type": "boolean"}, {"key": "medication_propranolol", "label": "15. <PERSON><PERSON><PERSON><PERSON><PERSON> (beta-bloqueador)", "type": "boolean"}, {"key": "medication_levothyroxine", "label": "16. <PERSON><PERSON><PERSON> T4/<PERSON><PERSON><PERSON><PERSON>a (Tratamento hipertireoidismo)", "type": "boolean"}, {"key": "medication_simvastatin", "label": "17. <PERSON><PERSON><PERSON><PERSON> (hipolipemiante)", "type": "boolean"}, {"key": "medication_valsartan", "label": "18. <PERSON><PERSON><PERSON>/<PERSON> (bloqueador do receptor AT)", "type": "boolean"}, {"key": "medication_not_reported", "label": "19. <PERSON><PERSON> relatado", "type": "boolean"}]}, {"title": "OUTRAS CONDIÇÕES", "description": "Por favor, assinale “SIM” ou “NÃO” às seguintes perguntas:", "requiredFields": ["other_pregnant", "other_smoker", "other_alcohol", "other_drugs", "other_leprosy", "other_tuberculosis", "other_hospitalization", "other_not_reported"], "fields": [{"key": "other_pregnant", "label": "1. <PERSON><PERSON><PERSON> gestante", "type": "boolean"}, {"key": "other_smoker", "label": "2. <PERSON><PERSON><PERSON> fuman<PERSON>", "type": "boolean"}, {"key": "other_alcohol", "label": "3. Faz uso de álcool", "type": "boolean"}, {"key": "other_drugs", "label": "4. <PERSON>az uso de outras drogas", "type": "boolean"}, {"key": "other_leprosy", "label": "5. Está com hanseníase", "type": "boolean"}, {"key": "other_tuberculosis", "label": "6. Está com tuberculose", "type": "boolean"}, {"key": "other_hospitalization", "label": "7. Teve alguma internação nos últimos 12 meses", "type": "boolean"}, {"key": "other_not_reported", "label": "8. <PERSON><PERSON> relatado", "type": "boolean"}]}, {"title": "CONDIÇÃO CARDIOVASCULAR", "description": "Por favor, selecione a que melhor atende:", "requiredFields": ["heart_rate", "heart_rhythm_alteration", "systolic_pa", "diastolic_pa", "sleep_hours", "sleep_quality"], "fields": [{"key": "heart_rate", "label": "FC (bpm) *", "type": "textfield"}, {"key": "heart_rhythm_alteration", "label": "Identifica alteração no ritmo cardíaco?*", "type": "boolean"}, {"key": "systolic_pa", "label": "PA sistólica (mmHg) *", "type": "textfield"}, {"key": "diastolic_pa", "label": "PA distólica (mmHg) *", "type": "textfield"}, {"key": "postprandial_glucose", "label": "Glicemia capilar pós prandial (mg/dl)", "type": "textfield"}, {"key": "sleep_hours", "type": "select", "templateOptions": {"label": "Horas de sono *", "options": [{"label": "-5 horas", "value": "-5"}, {"label": "5 a 6 horas", "value": "5-6"}, {"label": "6 a 7 horas", "value": "6-7"}, {"label": "8 horas ou mais", "value": "8+"}, {"label": "Não relatado", "value": "nao_relatado"}]}}, {"key": "sleep_quality", "type": "select", "templateOptions": {"label": "Qualidade de sono *", "options": [{"label": "Excelente", "value": "excelente"}, {"label": "Bo<PERSON>", "value": "boa"}, {"label": "Regular", "value": "regular"}, {"label": "<PERSON><PERSON><PERSON>", "value": "ruim"}]}}]}, {"title": "INDICADORES DE SAÚDE", "description": "Por favor, selecione a que melhor atende:", "requiredFields": ["weight", "height", "bmi_classification", "waist_circumference", "rce", "rce_classification", "healthy_food_frequency", "family_diabetes_history", "findrisk_score", "risk_classification"], "fields": [{"key": "weight", "label": "Peso (kg) *", "type": "textfield"}, {"key": "height", "label": "Altura (cm) *", "type": "textfield"}, {"key": "bmi", "label": "IMC (kg/m²)", "type": "calculated", "templateOptions": {"calculation": {"formula": "{{weight}} / (({{height}} / 100) * ({{height}} / 100))"}}}, {"key": "bmi_classification", "type": "select", "templateOptions": {"label": "Classificação *", "canEdit": false, "options": [{"label": "Baixo peso", "value": "Baixo peso"}, {"label": "Peso normal", "value": "Peso normal"}, {"label": "Sobrepeso", "value": "Sobrepeso"}, {"label": "Obesidade I", "value": "Obesidade I"}, {"label": "Obesidade II", "value": "Obesidade II"}, {"label": "Obesidade III", "value": "Obesidade III"}]}}, {"key": "waist_circumference", "label": "Circuferência da cintura (cm) *", "type": "textfield"}, {"key": "rce", "label": "RCE *", "type": "calculated", "templateOptions": {"calculation": {"formula": "{{waist_circumference}} / {{height}}"}}}, {"key": "rce_classification", "type": "select", "templateOptions": {"label": "Classificação *", "canEdit": false, "options": [{"label": "Normal", "value": "Normal"}, {"label": "Risco crescente", "value": "Risco crescente"}, {"label": "Risco alto", "value": "Risco alto"}]}}, {"key": "physical_activity_daily", "label": "Você pratica pelo menos 30 minutos de atividade física diária no trabalho e/ou durante o hor<PERSON><PERSON> de <PERSON> (incluindo as atividades diárias normais)?", "type": "boolean"}, {"key": "healthy_food_frequency", "type": "select", "templateOptions": {"label": "Com que frequência você come legumes, verduras,\nfrutas ou grãos?", "options": [{"label": "Todos os dias", "value": "todos_os_dias"}, {"label": "Não todos os dias", "value": "nao_todos_os_dias"}]}}, {"key": "regular_medication_for_high_blood_pressure", "label": "Você já tomou regularmente algum medicamento para pressão alta?", "type": "boolean"}, {"key": "high_blood_glucose_history", "label": "Alguma vez você já apresentou glicose alta no sangue (por exemplo, em um exame médico de rotina, durante uma do<PERSON>, durante gravidez)?", "type": "boolean"}, {"key": "family_diabetes_history", "type": "select", "templateOptions": {"label": "Algum membro de sua família ou parente próximo já foi diagnosticado com diabetes (tipo 1 ou tipo 2)?", "options": [{"label": "NÃO", "value": "nao"}, {"label": "Sim: av<PERSON>, tia, tio ou primo de 1° grau (exceto pai, mãe, irmão, irmã ou filhos)", "value": "sim_avos_tia_tio_primo"}, {"label": "Sim: pai, mãe, irm<PERSON>, irmã ou filho", "value": "sim_pai_mae_irma<PERSON>_irma_filho"}]}}, {"key": "findrisk_score", "label": "Pontuação FINDRISC *", "type": "calculated"}, {"key": "risk_classification", "type": "select", "templateOptions": {"label": "Classificação de risco*", "canEdit": false, "options": [{"label": "Baixo", "value": "baixo"}, {"label": "Levemente elevado", "value": "levemente_elevado"}, {"label": "Moderado", "value": "moderado"}, {"label": "Alto", "value": "alto"}, {"label": "Muito <PERSON>", "value": "muito_alto"}]}}]}, {"title": "TESTE DE APTIDÃO FÍSICA", "description": "Por favor, selecione a que melhor atende:", "requiredFields": ["regular_physical_activity"], "fields": [{"key": "regular_physical_activity", "type": "select", "templateOptions": {"label": "Pratica atividade física regular? *", "options": [{"label": "Não", "value": "não"}, {"label": "SIM, há menos de 3 meses", "value": "menos_3_meses"}, {"label": "SIM, entre 3 e 6 meses", "value": "entre_3_6_meses"}, {"label": "SIM, entre 6 meses e 1 ano", "value": "entre_6_meses_1_ano"}, {"label": "SIM, há mais de 1 ano", "value": "mais_1_ano"}, {"label": "Não relatado", "value": "não_relatado"}]}}, {"key": "sit_and_reach", "label": "Sentar e alcançar banco de Wells (cm)", "type": "textfield"}, {"key": "sit_and_reach_classification", "type": "select", "templateOptions": {"label": "Classificação", "canEdit": false, "options": [{"label": "Excelente", "value": "Excelente"}, {"label": "Bo<PERSON>", "value": "Bo<PERSON>"}, {"label": "Mediano", "value": "Mediano"}, {"label": "Fraco", "value": "Fraco"}, {"label": "<PERSON><PERSON>", "value": "<PERSON><PERSON>"}]}}, {"key": "push_ups", "label": "<PERSON><PERSON><PERSON> de frente", "type": "textfield"}, {"key": "push_ups_classification", "type": "select", "templateOptions": {"label": "Classificação", "canEdit": false, "options": [{"label": "Excelente", "value": "Excelente"}, {"label": "Bo<PERSON>", "value": "Bo<PERSON>"}, {"label": "Mediano", "value": "Mediano"}, {"label": "Fraco", "value": "Fraco"}, {"label": "<PERSON><PERSON>", "value": "<PERSON><PERSON>"}]}}, {"key": "six_min_walk_test", "label": "Teste de caminhada de 6 min", "type": "textfield"}, {"key": "six_min_walk_classification", "type": "select", "templateOptions": {"label": "Classificação", "canEdit": false, "options": [{"label": "Excelente", "value": "Excelente"}, {"label": "Bo<PERSON>", "value": "Bo<PERSON>"}, {"label": "Mediano", "value": "Mediano"}, {"label": "Fraco", "value": "Fraco"}, {"label": "<PERSON><PERSON>", "value": "<PERSON><PERSON>"}]}}, {"key": "two_min_stationary_march_test", "label": "Teste de marcha estacionária de 2 min", "type": "textfield"}, {"key": "two_min_stationary_march_classification", "type": "select", "templateOptions": {"label": "Classificação", "canEdit": false, "options": [{"label": "Excelente", "value": "Excelente"}, {"label": "Bo<PERSON>", "value": "Bo<PERSON>"}, {"label": "Mediano", "value": "Mediano"}, {"label": "Fraco", "value": "Fraco"}, {"label": "<PERSON><PERSON>", "value": "<PERSON><PERSON>"}]}}, {"key": "walk_test_1600m", "label": "Teste de caminhada de 1600 m", "type": "textfield"}, {"key": "walk_test_1600m_classification", "type": "select", "templateOptions": {"label": "Classificação *", "canEdit": false, "options": [{"label": "Superior", "value": "Superior"}, {"label": "Excelente", "value": "Excelente"}, {"label": "Bo<PERSON>", "value": "Bo<PERSON>"}, {"label": "Mediano", "value": "Mediano"}, {"label": "Fraco", "value": "Fraco"}, {"label": "<PERSON><PERSON>", "value": "<PERSON><PERSON>"}]}}, {"key": "walk_test_2400m", "label": "Teste de caminhada de 2400 m", "type": "textfield"}, {"key": "walk_test_2400m_classification", "type": "select", "templateOptions": {"label": "Classificação *", "canEdit": false, "options": [{"label": "Superior", "value": "Superior"}, {"label": "Excelente", "value": "Excelente"}, {"label": "Bo<PERSON>", "value": "Bo<PERSON>"}, {"label": "Mediano", "value": "Mediano"}, {"label": "Fraco", "value": "Fraco"}, {"label": "<PERSON><PERSON>", "value": "<PERSON><PERSON>"}]}}, {"key": "observations", "label": "Observações", "type": "textfield"}]}, {"title": "TESTE DE APTIDÃO FÍSICA (IDOSOS)", "description": "Por favor, selecione a que melhor atende:", "requiredFields": ["regular_physical_activity"], "fields": [{"key": "regular_physical_activity", "type": "select", "templateOptions": {"label": "Pratica atividade física regular? *", "options": [{"label": "Não", "value": "não"}, {"label": "SIM, há menos de 3 meses", "value": "menos_3_meses"}, {"label": "SIM, entre 3 e 6 meses", "value": "entre_3_6_meses"}, {"label": "SIM, entre 6 meses e 1 ano", "value": "entre_6_meses_1_ano"}, {"label": "SIM, há mais de 1 ano", "value": "mais_1_ano"}, {"label": "Não relatado", "value": "não_relatado"}]}}, {"key": "sit_and_reach", "label": "Sentar e alcançar - modificado (cm)", "type": "textfield"}, {"key": "sit_and_reach_classification", "type": "select", "templateOptions": {"label": "Classificação", "canEdit": false, "options": [{"label": "Excelente", "value": "Excelente"}, {"label": "Bo<PERSON>", "value": "Bo<PERSON>"}, {"label": "Mediano", "value": "Mediano"}, {"label": "Fraco", "value": "Fraco"}, {"label": "<PERSON><PERSON>", "value": "<PERSON><PERSON>"}]}}, {"key": "push_ups", "label": "<PERSON>lex<PERSON> de cotovelos (30s)", "type": "textfield"}, {"key": "push_ups_classification", "type": "select", "templateOptions": {"label": "Classificação", "canEdit": false, "options": [{"label": "Excelente", "value": "Excelente"}, {"label": "Bo<PERSON>", "value": "Bo<PERSON>"}, {"label": "Mediano", "value": "Mediano"}, {"label": "Fraco", "value": "Fraco"}, {"label": "<PERSON><PERSON>", "value": "<PERSON><PERSON>"}]}}, {"key": "sit_ups", "label": "<PERSON><PERSON> e levantar (30s)", "type": "textfield"}, {"key": "sit_ups_classification", "type": "select", "templateOptions": {"label": "Classificação", "canEdit": false, "options": [{"label": "Excelente", "value": "Excelente"}, {"label": "Bo<PERSON>", "value": "Bo<PERSON>"}, {"label": "Mediano", "value": "Mediano"}, {"label": "Fraco", "value": "Fraco"}, {"label": "<PERSON><PERSON>", "value": "<PERSON><PERSON>"}]}}, {"key": "six_min_walk_test", "label": "Teste de caminhada de 6 min", "type": "textfield"}, {"key": "six_min_walk_classification", "type": "select", "templateOptions": {"label": "Classificação", "canEdit": false, "options": [{"label": "Excelente", "value": "Excelente"}, {"label": "Bo<PERSON>", "value": "Bo<PERSON>"}, {"label": "Mediano", "value": "Mediano"}, {"label": "Fraco", "value": "Fraco"}, {"label": "<PERSON><PERSON>", "value": "<PERSON><PERSON>"}]}}, {"key": "two_min_stationary_march_test", "label": "Teste de marcha estacionária de 2 min", "type": "textfield"}, {"key": "two_min_stationary_march_classification", "type": "select", "templateOptions": {"label": "Classificação", "canEdit": false, "options": [{"label": "Excelente", "value": "Excelente"}, {"label": "Bo<PERSON>", "value": "Bo<PERSON>"}, {"label": "Mediano", "value": "Mediano"}, {"label": "Fraco", "value": "Fraco"}, {"label": "<PERSON><PERSON>", "value": "<PERSON><PERSON>"}]}}, {"key": "walk_test_1600m", "label": "Teste de caminhada de 1600 m", "type": "textfield"}, {"key": "walk_test_1600m_classification", "type": "select", "templateOptions": {"label": "Classificação *", "canEdit": false, "options": [{"label": "Superior", "value": "Superior"}, {"label": "Excelente", "value": "Excelente"}, {"label": "Bo<PERSON>", "value": "Bo<PERSON>"}, {"label": "Mediano", "value": "Mediano"}, {"label": "Fraco", "value": "Fraco"}, {"label": "<PERSON><PERSON>", "value": "<PERSON><PERSON>"}]}}, {"key": "walk_test_2400m", "label": "Teste de caminhada de 2400 m", "type": "textfield"}, {"key": "walk_test_2400m_classification", "type": "select", "templateOptions": {"label": "Classificação *", "canEdit": false, "options": [{"label": "Superior", "value": "Superior"}, {"label": "Excelente", "value": "Excelente"}, {"label": "Bo<PERSON>", "value": "Bo<PERSON>"}, {"label": "Mediano", "value": "Mediano"}, {"label": "Fraco", "value": "Fraco"}, {"label": "<PERSON><PERSON>", "value": "<PERSON><PERSON>"}]}}, {"key": "observations", "label": "Observações", "type": "textfield"}]}, {"title": "TIPO DE ATENDIMENTO", "description": "Por favor, selecione a que melhor atende:", "requiredFields": ["tipo_atendimento", "professional_name"], "fields": [{"key": "tipo_atendimento", "type": "select", "templateOptions": {"label": "Selecione o tipo de atendimento *", "options": [{"label": "Escuta inicial/Orientação", "value": "escuta_inicial"}, {"label": "Consulta do dia", "value": "consulta_dia"}, {"label": "Atendimento de urgência", "value": "urgencia"}, {"label": "Consulta agendada", "value": "<PERSON>da"}]}}, {"key": "professional_name", "type": "select", "templateOptions": {"label": "Nome do profissional *", "options": []}}, {"key": "professional_cns", "type": "textfield", "templateOptions": {"label": "CNS do profissional *", "canEdit": false}}]}]}