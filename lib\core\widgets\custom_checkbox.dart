import 'package:city_academy/theme/colors.dart';
import 'package:flutter/material.dart';

class CustomCheckbox extends StatelessWidget {
  final bool value;
  final ValueChanged<bool?>? onChanged;
  final double size;

  const CustomCheckbox({
    super.key,
    required this.value,
    required this.onChanged,
    this.size = 20,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        if (onChanged != null) {
          onChanged!(!value);
        }
      },
      child: Container(
        width: size,
        height: size,
        decoration: BoxDecoration(
          color: value ? CustomColors.greenBase : Colors.transparent,
          borderRadius: const BorderRadius.all(Radius.circular(4.0)),
          border: Border.all(
            color: value ? CustomColors.greenBase : CustomColors.neutralDark700,
            width: 2.0,
          ),
        ),
        child: Offstage(
            offstage: !value,
            child: Icon(
              Icons.check,
              weight: 10,
              size: size * 0.8,
              color: CustomColors.neutralWhite,
            )),
      ),
    );
  }
}
