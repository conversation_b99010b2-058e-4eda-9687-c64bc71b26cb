import 'package:city_academy/core/util/enums.dart';
import 'package:city_academy/core/util/functions/funcs.dart';
import 'package:city_academy/core/util/global_instances.dart';
import 'package:city_academy/routes/app_routes.dart';
import 'package:city_academy/screens/activity_list/model/activity_model.dart';
import 'package:city_academy/screens/event_city_admin/controller/event_city_admin_service.dart';
import 'package:city_academy/screens/event_city_admin/model/event_city_admin_model.dart';
import 'package:city_academy/screens/form/controller/form_service.dart';
import 'package:city_academy/screens/presence_form/controller/presence_form_service.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import 'meet_presence_state.dart';

class MeetPresenceCubit extends Cubit<MeetPresenceState> {
  MeetPresenceCubit() : super(MeetPresenceStateEmpty());

  Future<void> getCoaches() async {
    emit(state.copyWith(loading: true));
    try {
      final coaches = await FormService().fetchCoachs();
      emit(state.copyWith(coaches: coaches));
    } catch (e) {
      emit(state.copyWith(error: e.toString()));
    } finally {
      emit(state.copyWith(loading: false));
    }
  }

  void fetchActivity(String activityId) async {
    emit(state.copyWith(loading: true));
    try {
      final response = await PresenceFormService().getActivityDetails(
          loginCubit.state.user.activityCenterId!, activityId);

      emit(state.copyWith(eventCity: response));
    } catch (e) {
      emit(state.copyWith(error: e.toString()));
    } finally {
      emit(state.copyWith(loading: false));
    }
  }

  void setUserInConfirmation(String id) {
    emit(state.copyWith(listSelected: [id, ...state.listSelected]));
  }

  void removeUserInConfirmation(String id) {
    final listSelected = state.listSelected;
    listSelected.remove(id);
    emit(state.copyWith(listSelected: [...listSelected]));
  }

  void deleteActivity(ActivityModel activity) async {
    emit(state.copyWith(loading: true));

    try {
      final response = await EventCityAdminService().deleteActivity(
        loginCubit.state.user.activityCenterId!,
        activity.activityId,
      );

      if (response) {
        emit(state.copyWith(status: EventCityAdminStatus.succeeds));
        router.pop();
      } else {
        emit(state.copyWith(status: EventCityAdminStatus.failed));
      }
    } catch (e) {
      emit(
        state.copyWith(
          error: e.toString(),
          status: EventCityAdminStatus.failed,
        ),
      );
    } finally {
      emit(state.copyWith(loading: false));
    }
  }

  void onMeetingTopicChanged(int value) {
    emit(state.copyWith(meetingTopic: value));
  }

  bool isFormValid() {
    return state.meetingTopic != null && state.listSelected.isNotEmpty;
  }

  void action() async {
    emit(
      state.copyWith(
        loading: true,
        status: EventCityAdminStatus.none,
      ),
    );
    try {
      final updatedEventCity = state.eventCity.copyWith(
        atividadeTipo: 1,
        temasParaReuniao: [state.meetingTopic!],
        coachs: state.listSelected,
        coachId: loginCubit.state.user.id,
        turno: calculateTurn(state.eventCity.startTime),
      );

      await PresenceFormService().updateActivityDetails(updatedEventCity);

      emit(
        state.copyWith(
          loading: false,
          status: EventCityAdminStatus.succeeds,
        ),
      );
    } catch (e) {
      emit(
        state.copyWith(
          loading: false,
          error: e.toString(),
          status: EventCityAdminStatus.failed,
        ),
      );
    }
  }
}
