import 'dart:convert';

import 'package:city_academy/core/api/api_client.dart';
import 'package:city_academy/core/api/exceptions.dart';
import 'package:http/http.dart' as http;

class ApiResponseService {
  ApiResponse fromHttpResponse(http.Response response) {
    if (response.statusCode >= 200 && response.statusCode < 300) {
      dynamic data = {'responses': []};

      if (response.body.isNotEmpty) {
        try {
          final bodyDecoded = utf8.decode(response.bodyBytes);
          final decodedData = jsonDecode(bodyDecoded);

          if (decodedData is List) {
            data = {'responses': decodedData};
          } else if (decodedData is Map) {
            data = decodedData;
          } else if (decodedData is String || decodedData is num) {
            data = {'response': decodedData};
          } else {
            data = {'error': 'Invalid JSON format'};
          }
        } catch (e) {
          data = {'error': 'Invalid JSON format'};
        }
      }

      return ApiResponse(
        data: data,
        status: response.statusCode,
      );
    }

    throw errorHandler(response);
  }

  ApiException errorHandler(http.Response error) {
    String? message;
    if (error.body.isNotEmpty) {
      try {
        final decoded = jsonDecode(error.body);
        if (decoded is Map<String, dynamic>) {
          message =
              decoded['message'] ?? decoded['error'] ?? 'Erro desconhecido';
        } else if (decoded is String) {
          message = decoded;
        } else {
          message = 'Erro desconhecido';
        }
      } catch (e) {
        message = 'Erro desconhecido';
      }
    }

    if (error.statusCode >= 500) {
      return ServerException(error, message: message);
    }
    if (error.statusCode == 404) {
      return NotFoundException(error, message: message);
    }
    if (error.statusCode == 400) {
      return BadRequestException(error, message: message);
    }
    if (error.statusCode == 401) {
      return UnauthorizedException(error, message: message);
    }
    if (error.statusCode == 403) {
      return ForbiddenException(error, message: message);
    }

    return ApiException(error, message: message ?? 'Algo deu errado');
  }
}
