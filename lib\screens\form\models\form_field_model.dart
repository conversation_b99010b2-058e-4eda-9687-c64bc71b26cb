import 'package:city_academy/screens/form/models/form_data.dart';
import 'package:copy_with_extension/copy_with_extension.dart';

part 'form_field_model.g.dart';

@CopyWith()
class FormFieldModel {
  final String name;
  final String? label;
  final String type;
  final bool canEdit;
  final dynamic options;
  final String? value;
  final String? placeholder;
  final String? validation;
  final String? dependency;
  final Map<String, dynamic>? templateOptions;
  final Map<String, dynamic>? calculation;
  final FormData? formSection;

  FormFieldModel({
    required this.name,
    this.label,
    required this.type,
    this.canEdit = true,
    this.options,
    this.value,
    this.placeholder,
    this.validation,
    this.dependency,
    this.templateOptions,
    this.calculation,
    this.formSection,
  });

  factory FormFieldModel.fromJson(Map<String, dynamic> json) {
    final template = json['templateOptions'] as Map<String, dynamic>?;
    final section = json['formSection'] != null
        ? FormData.fromJson(json['formSection'] as Map<String, dynamic>)
        : null;
    return FormFieldModel(
      name: json['key'] as String,
      label: template?['label'] ?? json['label'] as String?,
      type: json['type'] as String,
      canEdit: template?['canEdit'] ?? json['canEdit'] ?? true,
      options: template?['options'] ?? json['options'],
      value: json['value'] as String?,
      placeholder: json['placeholder'] as String?,
      validation: json['validation'] as String?,
      dependency: json['dependency'] as String?,
      templateOptions: template,
      calculation: template?['calculation'] as Map<String, dynamic>?,
      formSection: section,
    );
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> json = {
      'key': name,
      'type': type,
    };

    if (value != null) {
      json['value'] = value;
    }

    if (templateOptions != null) {
      json['templateOptions'] = {
        ...templateOptions!,
        if (label != null) 'label': label,
        if (options != null) 'options': options,
      };
    } else {
      json['label'] = label;
      if (options != null) json['options'] = options;
    }

    if (placeholder != null) json['placeholder'] = placeholder;
    if (validation != null) json['validation'] = validation;
    if (dependency != null) json['dependency'] = dependency;

    if (formSection != null) {
      json['form'] = formSection!.toJson();
    }

    return json;
  }
}
