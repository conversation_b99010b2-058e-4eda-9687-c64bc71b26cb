import 'package:city_academy/core/shared/base_state.dart';
import 'package:city_academy/screens/history/model/response_form_data.dart';
import 'package:city_academy/screens/student_details/model/user_in_activity.dart';
import 'package:copy_with_extension/copy_with_extension.dart';
import 'package:empty_annotation/empty_annotation.dart';

part 'history_state.g.dart';

@CopyWith()
@Empty()
class HistoryState extends BaseState {
  final List<ResponseFormData> historyList;
  final UserInActivity student;

  HistoryState({
    required this.historyList,
    required this.student,
    required super.loading,
    required super.error,
    required super.message,
  });
}
