import 'package:city_academy/core/shared/base_service.dart';
import 'package:city_academy/screens/form/controller/form_repository.dart';
import 'package:city_academy/screens/form/models/form_data.dart';
import 'package:city_academy/screens/form/models/send_form_data_model.dart';
import 'package:city_academy/screens/login/model/coach.dart';

class FormService extends BaseService {
  final _repository = FormRepository();

  Future<FormData> fetchForm(String formId) {
    return super.call(() => _repository.fetchForm(formId));
  }

  Future<String> saveForm(SendFormDataModel model) {
    return super.call(() => _repository.saveForm(model));
  }

  Future<bool> updateForm(SendFormDataModel model) {
    return super.call(() => _repository.updateForm(model));
  }

  Future<List<FormData>> fetchFormResponseList(
    String formId,
    String userId,
  ) {
    return super.call(() => _repository.fetchFormResponseList(formId, userId));
  }

  Future<FormData> fetchFormResponse(
    String formId,
    String responseId,
  ) {
    return super.call(() => _repository.fetchFormResponse(formId, responseId));
  }

  Future<List<Coach>> fetchCoachs() {
    return super.call(() => _repository.fetchCoachs());
  }
}
