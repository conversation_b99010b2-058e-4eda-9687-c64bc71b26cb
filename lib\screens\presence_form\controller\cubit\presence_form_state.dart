import 'package:city_academy/core/util/enums.dart';
import 'package:city_academy/screens/event_city_admin/model/event_city_admin_model.dart';
import 'package:city_academy/screens/login/model/coach.dart';
import 'package:city_academy/screens/presence_form/model/presence_form_model.dart';
import 'package:copy_with_extension/copy_with_extension.dart';
import 'package:empty_annotation/empty_annotation.dart';

part 'presence_form_state.g.dart';

@CopyWith()
@Empty()
class PresenceFormState {
  final PresenceFormModel presenceFormModel;
  final List<Coach> coaches;
  final bool isLoading;
  final String? error;
  final EventCityAdminModel eventCity;
  final EventCityAdminStatus status;

  PresenceFormState({
    required this.presenceFormModel,
    required this.coaches,
    this.isLoading = false,
    this.error,
    required this.eventCity,
    required this.status,
  });
}
