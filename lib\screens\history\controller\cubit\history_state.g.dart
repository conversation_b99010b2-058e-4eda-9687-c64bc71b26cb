// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'history_state.dart';

// **************************************************************************
// CopyWithGenerator
// **************************************************************************

abstract class _$HistoryStateCWProxy {
  /// This function **does support** nullification of nullable fields. All `null` values passed to `non-nullable` fields will be ignored.
  ///
  /// Usage
  /// ```dart
  /// HistoryState(...).copyWith(id: 12, name: "My name")
  /// ````
  HistoryState call({
    List<ResponseFormData> historyList,
    UserInActivity student,
    bool loading,
    String error,
    String message,
  });
}

/// Proxy class for `copyWith` functionality. This is a callable class and can be used as follows: `instanceOfHistoryState.copyWith(...)`.
class _$HistoryStateCWProxyImpl implements _$HistoryStateCWProxy {
  const _$HistoryStateCWProxyImpl(this._value);

  final HistoryState _value;

  @override

  /// This function **does support** nullification of nullable fields. All `null` values passed to `non-nullable` fields will be ignored.
  ///
  /// Usage
  /// ```dart
  /// HistoryState(...).copyWith(id: 12, name: "My name")
  /// ````
  HistoryState call({
    Object? historyList = const $CopyWithPlaceholder(),
    Object? student = const $CopyWithPlaceholder(),
    Object? loading = const $CopyWithPlaceholder(),
    Object? error = const $CopyWithPlaceholder(),
    Object? message = const $CopyWithPlaceholder(),
  }) {
    return HistoryState(
      historyList: historyList == const $CopyWithPlaceholder()
          ? _value.historyList
          // ignore: cast_nullable_to_non_nullable
          : historyList as List<ResponseFormData>,
      student: student == const $CopyWithPlaceholder()
          ? _value.student
          // ignore: cast_nullable_to_non_nullable
          : student as UserInActivity,
      loading: loading == const $CopyWithPlaceholder()
          ? _value.loading
          // ignore: cast_nullable_to_non_nullable
          : loading as bool,
      error: error == const $CopyWithPlaceholder()
          ? _value.error
          // ignore: cast_nullable_to_non_nullable
          : error as String,
      message: message == const $CopyWithPlaceholder()
          ? _value.message
          // ignore: cast_nullable_to_non_nullable
          : message as String,
    );
  }
}

extension $HistoryStateCopyWith on HistoryState {
  /// Returns a callable class that can be used as follows: `instanceOfHistoryState.copyWith(...)`.
  // ignore: library_private_types_in_public_api
  _$HistoryStateCWProxy get copyWith => _$HistoryStateCWProxyImpl(this);
}

// **************************************************************************
// EmptyGenerator
// **************************************************************************

class HistoryStateEmpty extends HistoryState {
  HistoryStateEmpty()
      : super(
            historyList: const [],
            student: UserInActivityEmpty(),
            loading: false,
            error: '',
            message: '');
}
