import 'package:city_academy/core/util/enums.dart';
import 'package:city_academy/core/util/global_instances.dart';
import 'package:city_academy/core/util/image_path.dart';
import 'package:city_academy/core/widgets/appbar_custom.dart';
import 'package:city_academy/core/widgets/buttons/info_row_button.dart';
import 'package:city_academy/core/widgets/checkbox_tile.dart';
import 'package:city_academy/core/widgets/textfields/text_field_custom.dart';
import 'package:city_academy/routes/app_routes.dart';
import 'package:city_academy/routes/routes.dart';
import 'package:city_academy/screens/event_city_admin/model/event_city_admin_model.dart';
import 'package:city_academy/screens/search_student/controller/bloc/search_students_state.dart';
import 'package:city_academy/theme/colors.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class SearchStudentScreen extends StatefulWidget {
  final bool isFromAttendanceRegistration;
  final EventCityAdminModel? eventCity;

  const SearchStudentScreen({
    super.key,
    this.isFromAttendanceRegistration = false,
    this.eventCity,
  });

  @override
  SearchStudentScreenState createState() => SearchStudentScreenState();
}

class SearchStudentScreenState extends State<SearchStudentScreen> {
  @override
  void initState() {
    super.initState();
    searchStudentsBloc.fetchStudent();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: const AppbarCustom(title: 'Pesquisar aluno'),
      body: BlocBuilder(
        bloc: searchStudentsBloc,
        builder: (context, SearchStudentsState state) {
          return Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16.0),
            child: Column(
              children: [
                const SizedBox(height: 40),
                TextFieldCustom(
                  onChanged: searchStudentsBloc.updateQuery,
                  hintText: 'Search',
                  suffixIcon: const Icon(
                    Icons.search,
                    color: CustomColors.neutralGray500,
                  ),
                ),
                const SizedBox(height: 16),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                  children: [
                    Center(
                      child: CheckboxTile(
                        title: 'Buscar por nome',
                        value: state.type == SearchType.name,
                        onChanged: (_) =>
                            searchStudentsBloc.updateType(SearchType.name),
                      ),
                    ),
                    Center(
                      child: CheckboxTile(
                        title: 'Buscar por CPF',
                        value: state.type == SearchType.cpf,
                        onChanged: (_) =>
                            searchStudentsBloc.updateType(SearchType.cpf),
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 40),
                Expanded(
                    child: (state.students != null &&
                            (state.students?.isNotEmpty ?? false))
                        ? ListView.builder(
                            itemCount: state.students!.length,
                            itemBuilder: (context, index) {
                              return InfoRowButton(
                                onTap: () {
                                  if (widget.isFromAttendanceRegistration) {
                                    eventCityAdminCubit
                                        .setEventCity(widget.eventCity!);
                                    eventCityAdminCubit.addUserToConfirmation(
                                        state.students![index]);
                                    router.pop();
                                  } else {
                                    studentDetailsBloc
                                        .setUser(state.students![index]);
                                    return router.push(Routes.studentDetails);
                                  }
                                },
                                leadingIconColor: (state
                                                .students![index].diseases ??
                                            {})
                                        .isNotEmpty
                                    ? CustomColors.warningYellow
                                    : state.students![index].isComplete ?? false
                                        ? CustomColors.greenBase
                                        : CustomColors.errorRed,
                                leadingIcon: ImagePath.user,
                                title: state.students![index].name,
                              );
                            },
                          )
                        : const Center(child: Text('Nenhum aluno encontrado'))),
              ],
            ),
          );
        },
      ),
    );
  }
}
