// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'activity_model.dart';

// **************************************************************************
// CopyWithGenerator
// **************************************************************************

abstract class _$ActivityModelCWProxy {
  /// This function **does support** nullification of nullable fields. All `null` values passed to `non-nullable` fields will be ignored.
  ///
  /// Usage
  /// ```dart
  /// ActivityModel(...).copyWith(id: 12, name: "My name")
  /// ````
  ActivityModel call({
    String activityId,
    String activityCenterId,
    DateTime startTime,
    DateTime endTime,
    String description,
    String eventName,
  });
}

/// Proxy class for `copyWith` functionality. This is a callable class and can be used as follows: `instanceOfActivityModel.copyWith(...)`.
class _$ActivityModelCWProxyImpl implements _$ActivityModelCWProxy {
  const _$ActivityModelCWProxyImpl(this._value);

  final ActivityModel _value;

  @override

  /// This function **does support** nullification of nullable fields. All `null` values passed to `non-nullable` fields will be ignored.
  ///
  /// Usage
  /// ```dart
  /// ActivityModel(...).copyWith(id: 12, name: "My name")
  /// ````
  ActivityModel call({
    Object? activityId = const $CopyWithPlaceholder(),
    Object? activityCenterId = const $CopyWithPlaceholder(),
    Object? startTime = const $CopyWithPlaceholder(),
    Object? endTime = const $CopyWithPlaceholder(),
    Object? description = const $CopyWithPlaceholder(),
    Object? eventName = const $CopyWithPlaceholder(),
  }) {
    return ActivityModel(
      activityId: activityId == const $CopyWithPlaceholder()
          ? _value.activityId
          // ignore: cast_nullable_to_non_nullable
          : activityId as String,
      activityCenterId: activityCenterId == const $CopyWithPlaceholder()
          ? _value.activityCenterId
          // ignore: cast_nullable_to_non_nullable
          : activityCenterId as String,
      startTime: startTime == const $CopyWithPlaceholder()
          ? _value.startTime
          // ignore: cast_nullable_to_non_nullable
          : startTime as DateTime,
      endTime: endTime == const $CopyWithPlaceholder()
          ? _value.endTime
          // ignore: cast_nullable_to_non_nullable
          : endTime as DateTime,
      description: description == const $CopyWithPlaceholder()
          ? _value.description
          // ignore: cast_nullable_to_non_nullable
          : description as String,
      eventName: eventName == const $CopyWithPlaceholder()
          ? _value.eventName
          // ignore: cast_nullable_to_non_nullable
          : eventName as String,
    );
  }
}

extension $ActivityModelCopyWith on ActivityModel {
  /// Returns a callable class that can be used as follows: `instanceOfActivityModel.copyWith(...)`.
  // ignore: library_private_types_in_public_api
  _$ActivityModelCWProxy get copyWith => _$ActivityModelCWProxyImpl(this);
}

// **************************************************************************
// EmptyGenerator
// **************************************************************************

class ActivityModelEmpty extends ActivityModel {
  ActivityModelEmpty()
      : super(
            activityId: '',
            activityCenterId: '',
            startTime: DateTime.now(),
            endTime: DateTime.now(),
            description: '',
            eventName: '');
}
