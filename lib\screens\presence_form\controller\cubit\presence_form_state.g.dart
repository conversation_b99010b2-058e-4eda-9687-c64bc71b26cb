// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'presence_form_state.dart';

// **************************************************************************
// CopyWithGenerator
// **************************************************************************

abstract class _$PresenceFormStateCWProxy {
  /// This function **does support** nullification of nullable fields. All `null` values passed to `non-nullable` fields will be ignored.
  ///
  /// Usage
  /// ```dart
  /// PresenceFormState(...).copyWith(id: 12, name: "My name")
  /// ````
  PresenceFormState call({
    PresenceFormModel presenceFormModel,
    List<Coach> coaches,
    bool isLoading,
    String? error,
    EventCityAdminModel eventCity,
    EventCityAdminStatus status,
  });
}

/// Proxy class for `copyWith` functionality. This is a callable class and can be used as follows: `instanceOfPresenceFormState.copyWith(...)`.
class _$PresenceFormStateCWProxyImpl implements _$PresenceFormStateCWProxy {
  const _$PresenceFormStateCWProxyImpl(this._value);

  final PresenceFormState _value;

  @override

  /// This function **does support** nullification of nullable fields. All `null` values passed to `non-nullable` fields will be ignored.
  ///
  /// Usage
  /// ```dart
  /// PresenceFormState(...).copyWith(id: 12, name: "My name")
  /// ````
  PresenceFormState call({
    Object? presenceFormModel = const $CopyWithPlaceholder(),
    Object? coaches = const $CopyWithPlaceholder(),
    Object? isLoading = const $CopyWithPlaceholder(),
    Object? error = const $CopyWithPlaceholder(),
    Object? eventCity = const $CopyWithPlaceholder(),
    Object? status = const $CopyWithPlaceholder(),
  }) {
    return PresenceFormState(
      presenceFormModel: presenceFormModel == const $CopyWithPlaceholder()
          ? _value.presenceFormModel
          // ignore: cast_nullable_to_non_nullable
          : presenceFormModel as PresenceFormModel,
      coaches: coaches == const $CopyWithPlaceholder()
          ? _value.coaches
          // ignore: cast_nullable_to_non_nullable
          : coaches as List<Coach>,
      isLoading: isLoading == const $CopyWithPlaceholder()
          ? _value.isLoading
          // ignore: cast_nullable_to_non_nullable
          : isLoading as bool,
      error: error == const $CopyWithPlaceholder()
          ? _value.error
          // ignore: cast_nullable_to_non_nullable
          : error as String?,
      eventCity: eventCity == const $CopyWithPlaceholder()
          ? _value.eventCity
          // ignore: cast_nullable_to_non_nullable
          : eventCity as EventCityAdminModel,
      status: status == const $CopyWithPlaceholder()
          ? _value.status
          // ignore: cast_nullable_to_non_nullable
          : status as EventCityAdminStatus,
    );
  }
}

extension $PresenceFormStateCopyWith on PresenceFormState {
  /// Returns a callable class that can be used as follows: `instanceOfPresenceFormState.copyWith(...)`.
  // ignore: library_private_types_in_public_api
  _$PresenceFormStateCWProxy get copyWith =>
      _$PresenceFormStateCWProxyImpl(this);

  /// Copies the object with the specific fields set to `null`. If you pass `false` as a parameter, nothing will be done and it will be ignored. Don't do it. Prefer `copyWith(field: null)`.
  ///
  /// Usage
  /// ```dart
  /// PresenceFormState(...).copyWithNull(firstField: true, secondField: true)
  /// ````
  PresenceFormState copyWithNull({
    bool error = false,
  }) {
    return PresenceFormState(
      presenceFormModel: presenceFormModel,
      coaches: coaches,
      isLoading: isLoading,
      error: error == true ? null : this.error,
      eventCity: eventCity,
      status: status,
    );
  }
}

// **************************************************************************
// EmptyGenerator
// **************************************************************************

class PresenceFormStateEmpty extends PresenceFormState {
  PresenceFormStateEmpty()
      : super(
            presenceFormModel: PresenceFormModelEmpty(),
            coaches: const [],
            isLoading: false,
            error: null,
            eventCity: EventCityAdminModelEmpty(),
            status: EventCityAdminStatus.values.first);
}
