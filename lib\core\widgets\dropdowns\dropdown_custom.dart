import 'package:city_academy/theme/colors.dart';
import 'package:city_academy/theme/texts.dart';
import 'package:flutter/material.dart';

class DropdownCustom extends StatelessWidget {
  final String? errorMessage;
  final String? hintText;
  final bool isEnabled;
  final String? Function(dynamic)? validator;
  final Function(dynamic)? onChanged;
  final List<DropdownMenuItem<dynamic>>? items;
  final dynamic value;
  final String? label;

  const DropdownCustom({
    super.key,
    this.errorMessage,
    this.hintText,
    this.isEnabled = true,
    this.validator,
    this.onChanged,
    this.items,
    this.value,
    this.label,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if (label != null) ...[
          Text(
            label!,
            style: titleText18.copyWith(
              color: CustomColors.neutralDark900,
              fontWeight: FontWeight.w500,
            ),
          ),
          const SizedBox(height: 8),
        ],
        DropdownButtonFormField<dynamic>(
          value: value,
          items: items,
          onChanged: isEnabled ? onChanged : null,
          validator: validator,
          style: titleText12.copyWith(
            color: CustomColors.neutralDark700,
          ),
          dropdownColor: CustomColors.neutralWhite,
          decoration: InputDecoration(
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: const BorderSide(color: CustomColors.neutralGray200),
            ),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: const BorderSide(color: CustomColors.neutralGray200),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: const BorderSide(color: CustomColors.greenBase),
            ),
            hintText: hintText,
            hintStyle: titleText12.copyWith(color: CustomColors.neutralGray500),
            contentPadding:
                const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
            filled: true,
            fillColor: CustomColors.neutralWhite,
          ),
        ),
        if (errorMessage != null)
          Padding(
            padding: const EdgeInsets.only(top: 4),
            child: Text(
              errorMessage!,
              style: titleText12.copyWith(color: CustomColors.errorRed),
            ),
          ),
      ],
    );
  }
}
