import 'package:city_academy/core/util/app_texts.dart';
import 'package:city_academy/core/util/global_instances.dart';
import 'package:city_academy/screens/event_city_admin/model/event_city_admin_model.dart';

class PresenceFormRepository {
  Future<EventCityAdminModel> getActivityDetails(
    String centerId,
    String activityId,
  ) async {
    final response = await authClient.get(
      path: AppTexts.activityList(centerId) + activityId,
    );

    return EventCityAdminModel.fromJson(response.data);
  }

  Future<EventCityAdminModel> updateActivityDetails(
      EventCityAdminModel model) async {
    final response = await authClient.put(
      path: AppTexts.activityList(model.activityCenterId) + model.activityId,
      body: model.toJson(),
    );

    return EventCityAdminModel.fromJson(response.data);
  }
}
