import 'package:city_academy/core/util/global_instances.dart';
import 'package:city_academy/screens/history/controller/cubit/history_state.dart';
import 'package:city_academy/screens/history/controller/history_service.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class HistoryCubit extends Cubit<HistoryState> {
  HistoryCubit() : super(HistoryStateEmpty());

  fetchHistory(String formId) async {
    emit(state.copyWith(
      loading: true,
      student: studentDetailsBloc.state.student,
    ));

    try {
      final response =
          await HistoryService().fetchHistory(formId, state.student.id);

      emit(state.copyWith(historyList: response));
    } catch (e) {
      emit(state.copyWith(error: e.toString()));
    } finally {
      emit(state.copyWith(loading: false));
    }
  }
}
