import 'package:city_academy/core/util/enums.dart';
import 'package:city_academy/core/util/functions/classification_rule.dart';
import 'package:intl/intl.dart';
import 'package:mask_text_input_formatter/mask_text_input_formatter.dart';

final roleValuesMap = {
  "common": UserRole.common,
  "main": UserRole.main,
  "teacher_contractor": UserRole.teacherContractor,
};

final dateBirthMask = MaskTextInputFormatter(
  mask: '##/##/####',
  filter: {"#": RegExp(r'[0-9]')},
  type: MaskAutoCompletionType.lazy,
);

final dddMask = MaskTextInputFormatter(mask: '##');
final phoneNumberMask = MaskTextInputFormatter(mask: '# ####-####');
final sendPhoneNumberMask = MaskTextInputFormatter(mask: '(##)#####-####');

String sendDateMask(String date) {
  var dateFormat = DateFormat('dd/MM/yyyy');
  var dateTime = dateFormat.parse(date);
  var stringFormat = DateFormat('yyyy-MM-dd');
  return stringFormat.format(dateTime);
}

final DateFormat dateAndHours = DateFormat("yyyy-MM-dd HH:mm:ss");

DateTime endOfDay(DateTime date) {
  return DateTime(date.year, date.month, date.day, 23, 59, 59);
}

DateTime initOfDay(DateTime date) {
  return DateTime(date.year, date.month, date.day, 0, 0, 0);
}

final Map<int, String> activityType = {
  1: 'Atendimento em grupo',
  2: 'Educação em saúde',
};

final Map<int, String> targetAudience = {
  1: 'Comunidade em geral',
  2: 'Criança de 0 a 3 anos',
  3: 'Criança de 4 a 5 anos',
  4: 'Criança de 6 a 11 anos',
  5: 'Adolescente',
  6: 'Mulher',
  7: 'Gestante',
  8: 'Homem',
  9: 'Familiares',
  10: 'Pessoa idosa',
  12: 'Pessoas com doenças crônicas',
  13: 'Usuário de tabaco',
  14: 'Usuário de álcool',
  15: 'Usuário de outras drogas',
  16: 'Portador com sofrimento ou transtorno mental',
  17: 'Profissional da educação',
  18: 'Outros',
};

final Map<int, String> healthTopics = {
  29: 'Ações de combate ao Aedes aegypti',
  19: 'Agravos e Doenças Negligenciadas',
  1: 'Alimentação saudável',
  4: 'Autocuidado de pessoas com doenças crônicas',
  5: 'Cidadania e direitos humanos',
  7: 'Prevenção ao uso de álcool, tabaco e outras drogas',
  8: 'Envelhecimento / Climatério / Andropausa / Etc',
  10: 'Plantas medicinais / Fitoterapia',
  13: 'Prevenção da violência e promoção da cultura da paz',
  14: 'Saúde ambiental',
  15: 'Saúde bucal',
  6: 'Saúde do trabalhador',
  16: 'Saúde mental',
  17: 'Saúde sexual e reprodutiva',
  18: 'Semana saúde na escola',
  31: 'Amamentação',
  32: 'Alimentação complementar saudável',
  21: 'Outros',
};

final Map<int, String> healthProcedures = {
  20: 'Antropometria',
  2: 'Aplicação tópica de flúor',
  23: 'Desenvolvimento da linguagem',
  9: 'Escovação dental supervisionada',
  11: 'Práticas corporais e Atividade física',
  25: 'Programa Nacional de Controle do Tabagismo 1',
  26: 'Programa Nacional de Controle do Tabagismo 2',
  27: 'Programa Nacional de Controle do Tabagismo 3',
  28: 'Programa Nacional de Controle do Tabagismo 4',
  22: 'Saúde auditiva',
  3: 'Saúde ocular',
  24: 'Verificação da situação vacinal',
  12: 'Outras',
  30: 'Outro procedimento coletivo',
};

final Map<int, String> meetingTopics = {
  1: 'Questões administrativas / funcionamento',
  2: 'Processo de trabalho',
  3: 'Diagnóstico / Monitoramento do território',
  4: 'Planejamento / Monitoramento das ações da equipe',
  5: 'Discussão de caso ou projeto terapêutico singular',
  6: 'Educação permanente',
  7: 'Outros',
};

final Map<String, List<ClassificationRule>> classificationRules = {
  'bmi': [
    ClassificationRule(limit: 18.5, label: 'Baixo peso'),
    ClassificationRule(limit: 25, label: 'Peso normal'),
    ClassificationRule(limit: 30, label: 'Sobrepeso'),
    ClassificationRule(limit: 35, label: 'Obesidade I'),
    ClassificationRule(limit: 40, label: 'Obesidade II'),
    ClassificationRule(limit: double.infinity, label: 'Obesidade III'),
  ],
  'rce_men': [
    ClassificationRule(limit: 0.4851, label: 'Normal'),
    ClassificationRule(limit: 0.5451, label: 'Risco crescente'),
    ClassificationRule(limit: double.infinity, label: 'Risco alto'),
  ],
  'rce_women': [
    ClassificationRule(limit: 0.4651, label: 'Normal'),
    ClassificationRule(limit: 0.5551, label: 'Risco crescente'),
    ClassificationRule(limit: double.infinity, label: 'Risco alto'),
  ],
  'sit_and_reach_men_1_30': [
    ClassificationRule(limit: 15, label: 'Muito Fraco'),
    ClassificationRule(limit: 21, label: 'Fraco'),
    ClassificationRule(limit: 26, label: 'Mediano'),
    ClassificationRule(limit: 32, label: 'Bom'),
    ClassificationRule(limit: double.infinity, label: 'Excelente'),
  ],
  'sit_and_reach_men_40': [
    ClassificationRule(limit: 13, label: 'Muito Fraco'),
    ClassificationRule(limit: 18, label: 'Fraco'),
    ClassificationRule(limit: 24, label: 'Mediano'),
    ClassificationRule(limit: 30, label: 'Bom'),
    ClassificationRule(limit: double.infinity, label: 'Excelente'),
  ],
  'sit_and_reach_men_50': [
    ClassificationRule(limit: 10, label: 'Muito Fraco'),
    ClassificationRule(limit: 16, label: 'Fraco'),
    ClassificationRule(limit: 21, label: 'Mediano'),
    ClassificationRule(limit: 28, label: 'Bom'),
    ClassificationRule(limit: double.infinity, label: 'Excelente'),
  ],
  'sit_and_reach_women_1_30': [
    ClassificationRule(limit: 18, label: 'Muito Fraco'),
    ClassificationRule(limit: 24, label: 'Fraco'),
    ClassificationRule(limit: 29, label: 'Mediano'),
    ClassificationRule(limit: 35, label: 'Bom'),
    ClassificationRule(limit: double.infinity, label: 'Excelente'),
  ],
  'sit_and_reach_women_40': [
    ClassificationRule(limit: 19, label: 'Muito Fraco'),
    ClassificationRule(limit: 24, label: 'Fraco'),
    ClassificationRule(limit: 30, label: 'Mediano'),
    ClassificationRule(limit: 36, label: 'Bom'),
    ClassificationRule(limit: double.infinity, label: 'Excelente'),
  ],
  'sit_and_reach_women_50': [
    ClassificationRule(limit: 19, label: 'Muito Fraco'),
    ClassificationRule(limit: 24, label: 'Fraco'),
    ClassificationRule(limit: 29, label: 'Mediano'),
    ClassificationRule(limit: 35, label: 'Bom'),
    ClassificationRule(limit: double.infinity, label: 'Excelente'),
  ],
  'push_ups_women_1_30': [
    ClassificationRule(limit: 10, label: 'Muito Fraco'),
    ClassificationRule(limit: 15, label: 'Fraco'),
    ClassificationRule(limit: 21, label: 'Mediano'),
    ClassificationRule(limit: 30, label: 'Bom'),
    ClassificationRule(limit: double.infinity, label: 'Excelente'),
  ],
  'push_ups_women_40': [
    ClassificationRule(limit: 8, label: 'Muito Fraco'),
    ClassificationRule(limit: 13, label: 'Fraco'),
    ClassificationRule(limit: 20, label: 'Mediano'),
    ClassificationRule(limit: 27, label: 'Bom'),
    ClassificationRule(limit: double.infinity, label: 'Excelente'),
  ],
  'push_ups_women_50': [
    ClassificationRule(limit: 5, label: 'Muito Fraco'),
    ClassificationRule(limit: 11, label: 'Fraco'),
    ClassificationRule(limit: 15, label: 'Mediano'),
    ClassificationRule(limit: 24, label: 'Bom'),
    ClassificationRule(limit: double.infinity, label: 'Excelente'),
  ],
  'push_ups_men_1_30': [
    ClassificationRule(limit: 17, label: 'Muito Fraco'),
    ClassificationRule(limit: 22, label: 'Fraco'),
    ClassificationRule(limit: 29, label: 'Mediano'),
    ClassificationRule(limit: 36, label: 'Bom'),
    ClassificationRule(limit: double.infinity, label: 'Excelente'),
  ],
  'push_ups_men_40': [
    ClassificationRule(limit: 12, label: 'Muito Fraco'),
    ClassificationRule(limit: 17, label: 'Fraco'),
    ClassificationRule(limit: 22, label: 'Mediano'),
    ClassificationRule(limit: 30, label: 'Bom'),
    ClassificationRule(limit: double.infinity, label: 'Excelente'),
  ],
  'push_ups_men_50': [
    ClassificationRule(limit: 10, label: 'Muito Fraco'),
    ClassificationRule(limit: 13, label: 'Fraco'),
    ClassificationRule(limit: 17, label: 'Mediano'),
    ClassificationRule(limit: 25, label: 'Bom'),
    ClassificationRule(limit: double.infinity, label: 'Excelente'),
  ],
  'six_min_walk_men': [
    ClassificationRule(limit: 251, label: 'Muito Fraco'),
    ClassificationRule(limit: 318, label: 'Fraco'),
    ClassificationRule(limit: 394, label: 'Mediano'),
    ClassificationRule(limit: 458, label: 'Bom'),
    ClassificationRule(limit: double.infinity, label: 'Excelente'),
  ],
  'six_min_walk_women': [
    ClassificationRule(limit: 251, label: 'Muito Fraco'),
    ClassificationRule(limit: 318, label: 'Fraco'),
    ClassificationRule(limit: 394, label: 'Mediano'),
    ClassificationRule(limit: 458, label: 'Bom'),
    ClassificationRule(limit: double.infinity, label: 'Excelente'),
  ],
  'two_min_stationary_march_men': [
    ClassificationRule(limit: 47, label: 'Muito Fraco'),
    ClassificationRule(limit: 55, label: 'Fraco'),
    ClassificationRule(limit: 66, label: 'Mediano'),
    ClassificationRule(limit: 73, label: 'Bom'),
    ClassificationRule(limit: double.infinity, label: 'Excelente'),
  ],
  'two_min_stationary_march_women': [
    ClassificationRule(limit: 40, label: 'Muito Fraco'),
    ClassificationRule(limit: 52, label: 'Fraco'),
    ClassificationRule(limit: 62, label: 'Mediano'),
    ClassificationRule(limit: 74, label: 'Bom'),
    ClassificationRule(limit: double.infinity, label: 'Excelente'),
  ],
  'walk_test_female_20': [
    ClassificationRule(limit: 25.1, label: 'Muito Fraco'),
    ClassificationRule(limit: 31, label: 'Fraco'),
    ClassificationRule(limit: 35, label: 'Mediano'),
    ClassificationRule(limit: 39, label: 'Bom'),
    ClassificationRule(limit: 42, label: 'Excelente'),
    ClassificationRule(limit: double.infinity, label: 'Superior'),
  ],
  'walk_test_female_30': [
    ClassificationRule(limit: 23.7, label: 'Muito Fraco'),
    ClassificationRule(limit: 29, label: 'Fraco'),
    ClassificationRule(limit: 33, label: 'Mediano'),
    ClassificationRule(limit: 37, label: 'Bom'),
    ClassificationRule(limit: 41, label: 'Excelente'),
    ClassificationRule(limit: double.infinity, label: 'Superior'),
  ],
  'walk_test_female_40': [
    ClassificationRule(limit: 22.9, label: 'Muito Fraco'),
    ClassificationRule(limit: 27, label: 'Fraco'),
    ClassificationRule(limit: 31.5, label: 'Mediano'),
    ClassificationRule(limit: 35.7, label: 'Bom'),
    ClassificationRule(limit: 40.1, label: 'Excelente'),
    ClassificationRule(limit: double.infinity, label: 'Superior'),
  ],
  'walk_test_female_50': [
    ClassificationRule(limit: 21.1, label: 'Muito Fraco'),
    ClassificationRule(limit: 24.5, label: 'Fraco'),
    ClassificationRule(limit: 29, label: 'Mediano'),
    ClassificationRule(limit: 32.9, label: 'Bom'),
    ClassificationRule(limit: 37, label: 'Excelente'),
    ClassificationRule(limit: double.infinity, label: 'Superior'),
  ],
  'walk_test_female_60': [
    ClassificationRule(limit: 20.3, label: 'Muito Fraco'),
    ClassificationRule(limit: 22.8, label: 'Fraco'),
    ClassificationRule(limit: 27, label: 'Mediano'),
    ClassificationRule(limit: 31.5, label: 'Bom'),
    ClassificationRule(limit: 35.8, label: 'Excelente'),
    ClassificationRule(limit: double.infinity, label: 'Superior'),
  ],
  'walk_test_female_999': [
    ClassificationRule(limit: 17.6, label: 'Muito Fraco'),
    ClassificationRule(limit: 20.2, label: 'Fraco'),
    ClassificationRule(limit: 24.5, label: 'Mediano'),
    ClassificationRule(limit: 30.3, label: 'Bom'),
    ClassificationRule(limit: 31.5, label: 'Excelente'),
    ClassificationRule(limit: double.infinity, label: 'Superior'),
  ],
  'walk_test_male_20': [
    ClassificationRule(limit: 35.1, label: 'Muito Fraco'),
    ClassificationRule(limit: 38.4, label: 'Fraco'),
    ClassificationRule(limit: 45.2, label: 'Mediano'),
    ClassificationRule(limit: 51, label: 'Bom'),
    ClassificationRule(limit: 56, label: 'Excelente'),
    ClassificationRule(limit: double.infinity, label: 'Superior'),
  ],
  'walk_test_male_30': [
    ClassificationRule(limit: 33.1, label: 'Muito Fraco'),
    ClassificationRule(limit: 36.5, label: 'Fraco'),
    ClassificationRule(limit: 42.5, label: 'Mediano'),
    ClassificationRule(limit: 46.5, label: 'Bom'),
    ClassificationRule(limit: 52.5, label: 'Excelente'),
    ClassificationRule(limit: double.infinity, label: 'Superior'),
  ],
  'walk_test_male_40': [
    ClassificationRule(limit: 31.6, label: 'Muito Fraco'),
    ClassificationRule(limit: 35.5, label: 'Fraco'),
    ClassificationRule(limit: 41, label: 'Mediano'),
    ClassificationRule(limit: 45, label: 'Bom'),
    ClassificationRule(limit: 49.5, label: 'Excelente'),
    ClassificationRule(limit: double.infinity, label: 'Superior'),
  ],
  'walk_test_male_50': [
    ClassificationRule(limit: 30.3, label: 'Muito Fraco'),
    ClassificationRule(limit: 33.6, label: 'Fraco'),
    ClassificationRule(limit: 39, label: 'Mediano'),
    ClassificationRule(limit: 43.8, label: 'Bom'),
    ClassificationRule(limit: 48.1, label: 'Excelente'),
    ClassificationRule(limit: double.infinity, label: 'Superior'),
  ],
  'walk_test_male_60': [
    ClassificationRule(limit: 26.2, label: 'Muito Fraco'),
    ClassificationRule(limit: 31, label: 'Fraco'),
    ClassificationRule(limit: 35.8, label: 'Mediano'),
    ClassificationRule(limit: 41, label: 'Bom'),
    ClassificationRule(limit: 45.4, label: 'Excelente'),
    ClassificationRule(limit: double.infinity, label: 'Superior'),
  ],
  'walk_test_male_999': [
    ClassificationRule(limit: 20.6, label: 'Muito Fraco'),
    ClassificationRule(limit: 26.1, label: 'Fraco'),
    ClassificationRule(limit: 32.3, label: 'Mediano'),
    ClassificationRule(limit: 36.5, label: 'Bom'),
    ClassificationRule(limit: 44.3, label: 'Excelente'),
    ClassificationRule(limit: double.infinity, label: 'Superior'),
  ],
};
