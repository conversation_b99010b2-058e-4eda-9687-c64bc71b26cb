import 'package:city_academy/core/util/global_instances.dart';
import 'package:city_academy/core/widgets/appbar_custom.dart';
import 'package:city_academy/screens/health_indicators/controller/cubit/health_indicators_state.dart';
import 'package:city_academy/screens/health_indicators/view/widgets/blood_glucose_chart.dart';
import 'package:city_academy/screens/health_indicators/view/widgets/blood_pressure_chart.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class HealthIndicatorsScreen extends StatefulWidget {
  const HealthIndicatorsScreen({super.key});

  @override
  State<HealthIndicatorsScreen> createState() => _HealthIndicatorsScreenState();
}

class _HealthIndicatorsScreenState extends State<HealthIndicatorsScreen> {
  @override
  void initState() {
    super.initState();
    healthIndicatorsCubit.fetchHistoricalData();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: const AppbarCustom(title: 'Indicadores de saúde'),
      body: BlocBuilder(
        bloc: healthIndicatorsCubit,
        builder: (context, HealthIndicatorsState state) {
          if (state.loading) {
            return const Center(child: CircularProgressIndicator());
          }

          return const SingleChildScrollView(
            padding: EdgeInsets.symmetric(horizontal: 16.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                SizedBox(height: 40),
                BloodGlucoseChart(),
                SizedBox(height: 40),
                BloodPressureChart(),
              ],
            ),
          );
        },
      ),
    );
  }
}
