import 'package:city_academy/core/util/global_instances.dart';
import 'package:city_academy/core/widgets/base_form_screen.dart';
import 'package:city_academy/core/widgets/buttons/custom_button.dart';
import 'package:city_academy/core/widgets/snack_bar.dart';
import 'package:city_academy/routes/app_routes.dart';
import 'package:city_academy/screens/form/view/widgets/build_boolean.dart';
import 'package:city_academy/screens/form/view/widgets/build_calculated.dart';
import 'package:city_academy/screens/form/view/widgets/build_checkbox.dart';
import 'package:city_academy/screens/form/view/widgets/build_route.dart';
import 'package:city_academy/screens/form/view/widgets/build_select.dart';
import 'package:city_academy/screens/form/view/widgets/build_text.dart';
import 'package:city_academy/screens/form/view/widgets/build_textfield.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import '../controller/cubit/form_state.dart';
import '../models/form_data.dart';
import '../models/form_field_model.dart';

class DynamicFormScreen extends StatefulWidget {
  final bool isReadOnly;

  const DynamicFormScreen({super.key, this.isReadOnly = false});

  @override
  State<DynamicFormScreen> createState() => _DynamicFormScreenState();
}

class _DynamicFormScreenState extends State<DynamicFormScreen> {
  late Map<String, dynamic> extraData;

  @override
  void initState() {
    super.initState();
    extraData = router.state.extra as Map<String, dynamic>;
    formCubit.setFormData(extraData);
  }

  @override
  Widget build(BuildContext context) {
    return BlocBuilder(
      bloc: formCubit,
      builder: (context, FormScreenState state) {
        if (state.isLoading) {
          return const Center(child: CircularProgressIndicator());
        }

        final formData = state.formData;
        if (formData == null) {
          return const Center(child: Text('No form data available'));
        }

        return BaseFormScreen(
          title: formData.title,
          description: formData.description,
          children: [
            ..._buildFormFields(context, formData, state),
            if (!widget.isReadOnly) ...[
              const SizedBox(height: 24),
              if (state.formData?.title.toUpperCase() != 'DADOS PESSOAIS')
                CustomButton(
                  title: 'Salvar',
                  onPressed: () {
                    if (formCubit.validateForm()) {
                      formCubit.saveForm();
                      showCustomSnackbar(
                          context, 'Formulário salvo com sucesso');
                      router.pop();
                    } else {
                      showErrorSnackbar(
                        context,
                        'Preencha todos os campos obrigatórios',
                      );
                    }
                  },
                ),
              const SizedBox(height: 24),
            ]
          ],
        );
      },
    );
  }

  List<Widget> _buildFormFields(
    BuildContext context,
    FormData formData,
    FormScreenState state,
  ) {
    final List<Widget> widgets = [];

    for (final section in formData.sections) {
      for (final field in section.fields) {
        final hasSystolicPa =
            section.fields.any((f) => f.name == 'systolic_pa');
        final hasDiastolicPa =
            section.fields.any((f) => f.name == 'diastolic_pa');

        // Se ambos os campos estão presentes e o campo atual é diastolic_pa, pula
        // pois ele será renderizado junto com systolic_pa
        if (hasSystolicPa && hasDiastolicPa && field.name == 'diastolic_pa') {
          continue;
        }

        widgets.add(_buildField(context, field, state));
      }
    }

    return widgets;
  }

  Widget _buildField(
    BuildContext context,
    FormFieldModel field,
    FormScreenState state,
  ) {
    switch (field.type) {
      case 'textfield':
        return buildTextField(field, widget.isReadOnly);
      case 'select':
        return buildSelect(field, state, widget.isReadOnly);
      case 'checkbox':
        return buildCheckbox(field, state, widget.isReadOnly);
      case 'boolean':
        return buildBoolean(field, state, widget.isReadOnly);
      case 'route':
        return buildRoute(context, field, extraData);
      case 'text':
        return buildText(field);
      case 'calculated':
        return buildCalculated(field, state);
      default:
        return const SizedBox.shrink();
    }
  }
}
