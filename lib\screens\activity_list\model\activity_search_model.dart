import 'package:city_academy/core/util/enums.dart';
import 'package:city_academy/core/util/extentions.dart';

class ActivitySearchModel {
  final String centerId;
  final DateTime initialDate;
  final DateTime finalDate;
  final ActivityType activityType;

  ActivitySearchModel({
    required this.centerId,
    required this.initialDate,
    required this.finalDate,
    required this.activityType,
  });

  Map<String, dynamic> toJson() {
    return {
      'page_size': '100',
      'activity_time_start': initialDate.toIso8601String(),
      'activity_time_end': finalDate.toIso8601String(),
      'active': 'true',
      'type': activityType.toStringRepresentation(),
    };
  }
}
