import 'package:city_academy/core/shared/base_state.dart';
import 'package:city_academy/screens/login/model/user.dart';
import 'package:copy_with_extension/copy_with_extension.dart';
import 'package:empty_annotation/empty_annotation.dart';

part 'login_state.g.dart';

@Empty()
@CopyWith()
class LoginState extends BaseState {
  final String email;
  final String password;
  final User user;

  LoginState({
    required super.loading,
    required super.error,
    required super.message,
    required this.email,
    required this.password,
    required this.user,
  });
}
