import 'package:city_academy/screens/form/models/form_field_model.dart';
import 'package:city_academy/theme/colors.dart';
import 'package:city_academy/theme/texts.dart';
import 'package:flutter/material.dart';

Widget buildText(FormFieldModel field) {
  return Padding(
    padding: const EdgeInsets.only(bottom: 16.0),
    child: Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          field.label ?? '',
          style: titleText18.copyWith(
            color: CustomColors.neutralDark900,
            fontWeight: FontWeight.w500,
          ),
        ),
        Text(
          field.value ?? '',
          style: titleText16.copyWith(
            color: CustomColors.neutralGray500,
          ),
        ),
      ],
    ),
  );
}
