import 'package:city_academy/core/util/validators.dart';
import 'package:city_academy/core/widgets/textfields/text_field_custom.dart';
import 'package:flutter/material.dart';

class EmailTextField extends TextFieldCustom {
  const EmailTextField({
    super.key,
    super.onChanged,
    super.errorMessage,
    super.isEnabled,
    super.controller,
    super.initialValue,
    super.textInputAction,
  }) : super(
          hintText: 'Email',
          keyboardType: TextInputType.emailAddress,
          validator: validateEmail,
        );
}
