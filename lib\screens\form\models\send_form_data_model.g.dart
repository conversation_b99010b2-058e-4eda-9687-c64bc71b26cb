// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'send_form_data_model.dart';

// **************************************************************************
// CopyWithGenerator
// **************************************************************************

abstract class _$SendFormDataModelCWProxy {
  /// This function **does support** nullification of nullable fields. All `null` values passed to `non-nullable` fields will be ignored.
  ///
  /// Usage
  /// ```dart
  /// SendFormDataModel(...).copyWith(id: 12, name: "My name")
  /// ````
  SendFormDataModel call({
    String formId,
    FormData formData,
    String? traineeId,
    String? responseId,
  });
}

/// Proxy class for `copyWith` functionality. This is a callable class and can be used as follows: `instanceOfSendFormDataModel.copyWith(...)`.
class _$SendFormDataModelCWProxyImpl implements _$SendFormDataModelCWProxy {
  const _$SendFormDataModelCWProxyImpl(this._value);

  final SendFormDataModel _value;

  @override

  /// This function **does support** nullification of nullable fields. All `null` values passed to `non-nullable` fields will be ignored.
  ///
  /// Usage
  /// ```dart
  /// SendFormDataModel(...).copyWith(id: 12, name: "My name")
  /// ````
  SendFormDataModel call({
    Object? formId = const $CopyWithPlaceholder(),
    Object? formData = const $CopyWithPlaceholder(),
    Object? traineeId = const $CopyWithPlaceholder(),
    Object? responseId = const $CopyWithPlaceholder(),
  }) {
    return SendFormDataModel(
      formId: formId == const $CopyWithPlaceholder()
          ? _value.formId
          // ignore: cast_nullable_to_non_nullable
          : formId as String,
      formData: formData == const $CopyWithPlaceholder()
          ? _value.formData
          // ignore: cast_nullable_to_non_nullable
          : formData as FormData,
      traineeId: traineeId == const $CopyWithPlaceholder()
          ? _value.traineeId
          // ignore: cast_nullable_to_non_nullable
          : traineeId as String?,
      responseId: responseId == const $CopyWithPlaceholder()
          ? _value.responseId
          // ignore: cast_nullable_to_non_nullable
          : responseId as String?,
    );
  }
}

extension $SendFormDataModelCopyWith on SendFormDataModel {
  /// Returns a callable class that can be used as follows: `instanceOfSendFormDataModel.copyWith(...)`.
  // ignore: library_private_types_in_public_api
  _$SendFormDataModelCWProxy get copyWith =>
      _$SendFormDataModelCWProxyImpl(this);

  /// Copies the object with the specific fields set to `null`. If you pass `false` as a parameter, nothing will be done and it will be ignored. Don't do it. Prefer `copyWith(field: null)`.
  ///
  /// Usage
  /// ```dart
  /// SendFormDataModel(...).copyWithNull(firstField: true, secondField: true)
  /// ````
  SendFormDataModel copyWithNull({
    bool traineeId = false,
    bool responseId = false,
  }) {
    return SendFormDataModel(
      formId: formId,
      formData: formData,
      traineeId: traineeId == true ? null : this.traineeId,
      responseId: responseId == true ? null : this.responseId,
    );
  }
}
