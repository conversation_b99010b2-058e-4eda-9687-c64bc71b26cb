import 'package:city_academy/screens/login/model/user.dart';
import 'package:city_academy/screens/student_details/model/user_in_activity.dart';

User mapUserInActivityToUser(UserInActivity userInActivity) {
  return User(
    id: userInActivity.id,
    sub: userInActivity.id,
    type: 'user',
    active: true,
    createdAt: DateTime.now(),
    updatedAt: DateTime.now(),
    profileImageId: userInActivity.profileImageId,
    firstName: userInActivity.name.split(' ').first,
    lastName: userInActivity.name.split(' ').last,
    nickname: userInActivity.name,
    email: userInActivity.email,
    phone: userInActivity.phone,
    gender: userInActivity.gender,
    genderIdentity: userInActivity.genderIdentity,
    race: 'Outro',
    sexualOrientation: 'Outro',
    birthDate: userInActivity.birthDate,
    address: {},
    activityCenterId: '103',
    contacts: [userInActivity.emergencyContact ?? 'não informado'],
  );
}
