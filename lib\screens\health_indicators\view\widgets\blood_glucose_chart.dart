import 'package:fl_chart/fl_chart.dart';
import 'package:flutter/material.dart';

class BloodGlucoseChart extends StatelessWidget {
  const BloodGlucoseChart({super.key});

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 6,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text('Glicemia',
                style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold)),
            const Text('Maio - 2025', style: TextStyle(color: Colors.grey)),
            const SizedBox(height: 16),
            SizedBox(
              height: 200,
              child: BarChart(
                BarChartData(
                  borderData: FlBorderData(show: false),
                  gridData: const FlGridData(show: true),
                  titlesData: FlTitlesData(
                    leftTitles: const AxisTitles(
                      axisNameWidget: Text('Valor mg/dL'),
                      sideTitles: SideTitles(
                        showTitles: true,
                        reservedSize: 40,
                      ),
                    ),
                    bottomTitles: AxisTitles(
                      axisNameWidget: const Text('Data'),
                      sideTitles: SideTitles(
                        showTitles: true,
                        getTitlesWidget: (value, meta) {
                          return Text(
                            value.toInt().toString().padLeft(2, '0'),
                            style: const TextStyle(fontSize: 10),
                          );
                        },
                      ),
                    ),
                  ),
                  barGroups: [
                    BarChartGroupData(x: 1, barRods: [
                      BarChartRodData(toY: 110, color: Colors.green)
                    ]),
                    BarChartGroupData(x: 2, barRods: [
                      BarChartRodData(toY: 120, color: Colors.green)
                    ]),
                    BarChartGroupData(x: 3, barRods: [
                      BarChartRodData(toY: 95, color: Colors.green)
                    ]),
                    BarChartGroupData(x: 4, barRods: [
                      BarChartRodData(toY: 100, color: Colors.green)
                    ]),
                    BarChartGroupData(x: 5, barRods: [
                      BarChartRodData(toY: 130, color: Colors.green)
                    ]),
                    BarChartGroupData(x: 6, barRods: [
                      BarChartRodData(toY: 100, color: Colors.green)
                    ]),
                    BarChartGroupData(x: 7, barRods: [
                      BarChartRodData(toY: 110, color: Colors.green)
                    ]),
                    BarChartGroupData(x: 8, barRods: [
                      BarChartRodData(toY: 105, color: Colors.green)
                    ]),
                    BarChartGroupData(x: 9, barRods: [
                      BarChartRodData(toY: 100, color: Colors.green)
                    ]),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
