import 'package:city_academy/screens/health_indicators/model/measurement_data.dart';

class MonitoringService {
  // Método para buscar dados históricos de aferição
  // Em um cenário real, isso viria de uma API ou banco de dados
  Future<List<MeasurementData>> fetchHistoricalData() async {
    // Simulando um atraso de rede
    await Future.delayed(const Duration(milliseconds: 500));

    // Dados de exemplo para os últimos 7 dias
    final now = DateTime.now();
    return [
      MeasurementData(
        date: now.subtract(const Duration(days: 6)),
        bloodGlucose: 120,
        systolicPressure: 130,
        diastolicPressure: 85,
      ),
      MeasurementData(
        date: now.subtract(const Duration(days: 5)),
        bloodGlucose: 115,
        systolicPressure: 128,
        diastolicPressure: 82,
      ),
      MeasurementData(
        date: now.subtract(const Duration(days: 4)),
        bloodGlucose: 125,
        systolicPressure: 135,
        diastolicPressure: 88,
      ),
      MeasurementData(
        date: now.subtract(const Duration(days: 3)),
        bloodGlucose: 118,
        systolicPressure: 132,
        diastolicPressure: 84,
      ),
      MeasurementData(
        date: now.subtract(const Duration(days: 2)),
        bloodGlucose: 122,
        systolicPressure: 129,
        diastolicPressure: 83,
      ),
      MeasurementData(
        date: now.subtract(const Duration(days: 1)),
        bloodGlucose: 130,
        systolicPressure: 138,
        diastolicPressure: 87,
      ),
      MeasurementData(
        date: now,
        bloodGlucose: 124,
        systolicPressure: 133,
        diastolicPressure: 86,
      ),
    ];
  }
}
