import 'package:city_academy/core/util/app_texts.dart';
import 'package:city_academy/core/util/global_instances.dart';
import 'package:city_academy/screens/history/model/response_form_data.dart';

class HistoryRepository {
  Future<List<ResponseFormData>> fetchHistory(
    String formId,
    String? userId,
  ) async {
    final response = await authClient.get(
      path: AppTexts.formsResponse(formId),
      query: {'user_id': userId},
    );

    return (response.data['responses'] as List)
        .map((e) => ResponseFormData.fromJson(e))
        .toList();
  }
}
