import 'package:city_academy/core/shared/base_service.dart';
import 'package:city_academy/screens/event_city_admin/controller/event_city_admin_repository.dart';
import 'package:city_academy/screens/student_details/model/user_in_activity.dart';

class EventCityAdminService extends BaseService {
  final _repository = EventCityAdminRepository();

  Future<bool> deleteActivity(
    String centerId,
    String activityId,
  ) {
    return super.call(() => _repository.deleteActivity(centerId, activityId));
  }

  Future<bool> confirmationEventInCity(
    String activityId,
    List<num> usersConfirmed,
  ) {
    return super.call(() => _repository.confirmationEventInCity(
          activityId,
          usersConfirmed,
        ));
  }

  Future<List<UserInActivity>> fetchUsers({
    String? dayPeriod,
    List<String>? usersIds,
  }) {
    return super.call(
      () => _repository.fetchUsers(
        dayPeriod: dayPeriod,
        usersIds: usersIds,
      ),
    );
  }
}
