import 'package:city_academy/core/util/enums.dart';
import 'package:city_academy/core/util/extentions.dart';
import 'package:city_academy/core/util/functions/classification_rule.dart';
import 'package:city_academy/core/util/functions/findrisc_score.dart';
import 'package:city_academy/core/util/global_instances.dart';
import 'package:city_academy/screens/form/controller/form_service.dart';
import 'package:city_academy/screens/form/models/form_field_model.dart';
import 'package:city_academy/screens/form/models/form_section.dart';
import 'package:city_academy/screens/form/models/send_form_data_model.dart';
import 'package:city_academy/screens/form/view/widgets/build_calculated.dart';
import 'package:city_academy/screens/login/model/user.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import '../../models/form_data.dart';
import 'form_state.dart';

class FormCubit extends Cubit<FormScreenState> {
  FormCubit() : super(FormScreenStateEmpty());

  Future<void> loadFormData({String? file}) async {
    try {
      emit(state.copyWith(isLoading: true, file: file));

      FormData formData;

      if (state.responseId != null && state.file != null) {
        formData = await FormService().fetchFormResponse(
          state.file!,
          state.responseId!,
        );
      } else {
        formData = await FormService().fetchForm(state.file ?? '');
      }

      emit(state.copyWith(
        isLoading: false,
        formData: formData,
        globalFormData: formData,
      ));

      initializeFormValuesFromData(formData);
    } catch (e) {
      debugPrint('Erro ao carregar formulário: $e');
      emit(state.copyWith(isLoading: false));
    }
  }

  Future<void> setFormData(Map<String, dynamic> data) async {
    try {
      emit(state.copyWith(isLoading: true));

      if (data.containsKey('sections')) {
        final formData = FormData.fromJson(data);

        emit(state.copyWith(
          isLoading: false,
          formData: formData,
        ));
        initializeFormValuesFromData(formData);
      }
    } catch (e) {
      debugPrint('Error setting form data: $e');
      emit(state.copyWith(isLoading: false));
    }
  }

  Future<void> fetchFormResponseList(String formId) async {
    try {
      emit(state.copyWith(isLoading: true));

      final formResponses =
          await FormService().fetchFormResponseList(formId, state.user.id);

      emit(state.copyWith(
        isLoading: false,
        formResponses: formResponses,
      ));
    } catch (e) {
      debugPrint('Erro ao carregar respostas do formulário: $e');
      emit(state.copyWith(isLoading: false));
    }
  }

  Future<void> setResponseId(String value) async {
    emit(state.copyWith(responseId: value));
  }

  void initializeFormValuesFromData(FormData formData) async {
    final Map<String, dynamic> values = {};
    final Map<String, bool> checkboxes = {};
    final Map<String, String> booleans = {};

    for (final section in formData.sections) {
      if (section.title.toLowerCase() == 'informações do polo') {
        final updatedFields = section.fields.map((field) {
          if (field.name == 'polo') {
            return field.copyWith(
                options: homeCubit.state.activityCenterList
                    .map((e) => {'label': e.activityCenterName, 'value': e.id})
                    .toList());
          }
          return field;
        }).toList();

        final updatedSection = section.copyWith(fields: updatedFields);

        final updatedSections = formData.sections.map((s) {
          if (s.title.toLowerCase() == 'informações do polo') {
            return updatedSection;
          }
          return s;
        }).toList();

        emit(state.copyWith(
          formData: state.formData?.copyWith(sections: updatedSections),
        ));
      }
      if (section.title.toUpperCase() == 'TIPO DE ATENDIMENTO') {
        emit(state.copyWith(isLoading: true));
        final response = await FormService().fetchCoachs();
        final updatedFields = section.fields.map((field) {
          if (field.name == 'professional_name') {
            return field.copyWith(
                options: (response)
                    .map((e) => {'label': e.name, 'value': e.id})
                    .toList());
          }
          return field;
        }).toList();

        final updatedSection = section.copyWith(fields: updatedFields);

        final updatedSections = formData.sections.map((s) {
          if (s.title.toUpperCase() == 'TIPO DE ATENDIMENTO') {
            return updatedSection;
          }
          return s;
        }).toList();

        emit(state.copyWith(
          formData: state.formData?.copyWith(sections: updatedSections),
          isLoading: false,
          coachs: response,
        ));
      }
      if (section.title.toUpperCase() == 'DADOS PESSOAIS') {
        final updatedFields = section.fields.map((field) {
          // Preenche os campos com os dados do usuário
          switch (field.name) {
            case 'email':
              return field.copyWith(
                  value: field.value?.isEmpty ?? true
                      ? state.user.email
                      : field.value);
            case 'data_nascimento':
              return field.copyWith(
                  value: field.value?.isEmpty ?? true
                      ? state.user.birthDate.formatExibition()
                      : field.value);
            case 'contato_emergencia':
              return field.copyWith(
                value: (field.value?.isEmpty ?? true)
                    ? (state.user.contacts?.isNotEmpty ?? false)
                        ? '${state.user.contacts![0]['name']}: ${state.user.contacts![0]['value']}'
                        : ''
                    : field.value,
              );
            case 'telefone':
              return field.copyWith(
                  value: field.value?.isEmpty ?? true
                      ? state.user.phone
                      : field.value);
            case 'raca':
              return field.copyWith(
                  value: field.value?.isEmpty ?? true
                      ? state.user.race
                      : field.value);
            case 'orientacao_sexual':
              return field.copyWith(
                  value: field.value?.isEmpty ?? true
                      ? state.user.sexualOrientation
                      : field.value);
            case 'identidade_de_genero':
              return field.copyWith(
                  value: field.value?.isEmpty ?? true
                      ? state.user.genderIdentity
                      : field.value);
            case 'endereco':
              return field.copyWith(
                  value: field.value?.isEmpty ?? true ? 'Outro' : field.value);
            default:
              return field;
          }
        }).toList();

        // Atualiza a seção com os campos preenchidos
        final updatedSection = section.copyWith(fields: updatedFields);

        final updatedSections = formData.sections.map((s) {
          if (s.title.toUpperCase() == 'DADOS PESSOAIS') {
            return updatedSection;
          }
          return s;
        }).toList();

        emit(state.copyWith(
          formData: state.formData?.copyWith(sections: updatedSections),
        ));
      }

      for (final field in section.fields) {
        final value = field.value;
        if (value != null) {
          switch (field.type) {
            case 'checkbox':
              checkboxes[field.name] = value == 'sim' || value == 'true';
              break;
            case 'boolean':
              if (value == 'sim' || value == 'nao') {
                booleans[field.name] = value;
              }
              break;
            default:
              values[field.name] = value;
          }
        }
      }
    }

    emit(state.copyWith(
      formValues: values,
      checkboxValues: checkboxes,
      booleanSelections: booleans,
    ));
  }

  void updateFieldValue(String key, dynamic value) {
    final updatedValues = Map<String, dynamic>.from(state.formValues);
    updatedValues[key] = value;

    for (final field
        in (state.formData?.sections[0].fields ?? [] as List<FormFieldModel>)) {
      if (field.type == 'calculated') {
        final formula = field.templateOptions?['calculation']?['formula'] ?? '';
        final result = evaluateFormula(formula, updatedValues);
        final calculatedValue = double.tryParse(result) ?? 0.0;

        updatedValues[field.name] = calculatedValue;

        // Atualizar classificação de BMI, RCE, etc
        if (field.name == 'bmi') {
          updatedValues['bmi_classification'] =
              getClassification('bmi', calculatedValue);
        }

        if (field.name == 'rce') {
          final gender = state.user.gender;
          updatedValues['rce_classification'] =
              getClassification('rce', calculatedValue, gender: gender.name);
        }
      }
    }

    if (key == 'professional_name') {
      final coach = state.coachs?.firstWhere((c) => c.id == value);
      updatedValues['professional_cns'] = coach?.cns ?? '';
    }

    if (key == 'polo') {
      final activityCenter =
          homeCubit.state.activityCenterList.firstWhere((c) => c.id == value);
      updatedValues['distrito_sanitario'] = activityCenter.district ?? '';
      updatedValues['cns_polo'] = activityCenter.cnes ?? '';
    }

    if (key == 'sit_and_reach') {
      final gender = state.user.gender;
      final birthDate = state.user.birthDate;
      final parsedValue = double.tryParse(value.toString()) ?? 0.0;
      updatedValues['sit_and_reach_classification'] = getClassification(
        'sit_and_reach',
        parsedValue,
        gender: gender.name,
        birthDate: birthDate,
      );
    }

    if (key == 'push_ups') {
      final gender = state.user.gender;
      final birthDate = state.user.birthDate;
      final parsedValue = double.tryParse(value.toString()) ?? 0.0;
      updatedValues['push_ups_classification'] = getClassification(
        'push_ups',
        parsedValue,
        gender: gender.name,
        birthDate: birthDate,
      );
    }

    if (key == 'six_min_walk_test') {
      final gender = state.user.gender;
      final birthDate = state.user.birthDate;
      final parsedValue = double.tryParse(value.toString()) ?? 0.0;
      updatedValues['six_min_walk_classification'] = getClassification(
        'six_min_walk_test',
        parsedValue,
        gender: gender.name,
        birthDate: birthDate,
      );
    }

    if (key == 'two_min_stationary_march_test') {
      final gender = state.user.gender;
      final birthDate = state.user.birthDate;
      final parsedValue = double.tryParse(value.toString()) ?? 0.0;
      updatedValues['two_min_stationary_march_classification'] =
          getClassification(
        'two_min_stationary_march_test',
        parsedValue,
        gender: gender.name,
        birthDate: birthDate,
      );
    }

    if (key == 'walk_test_1600m') {
      final gender = state.user.gender;
      final birthDate = state.user.birthDate;
      final parsedValue = double.tryParse(value.toString()) ?? 0.0;
      updatedValues['walk_test_1600m_classification'] = getClassification(
        'walk_test',
        parsedValue,
        gender: gender.name,
        birthDate: birthDate,
      );
    }

    if (key == 'walk_test_2400m') {
      final gender = state.user.gender;
      final birthDate = state.user.birthDate;
      final parsedValue = double.tryParse(value.toString()) ?? 0.0;
      updatedValues['walk_test_2400m_classification'] = getClassification(
        'walk_test',
        parsedValue,
        gender: gender.name,
        birthDate: birthDate,
      );
    }

    final tempState = state.copyWith(
      formValues: updatedValues,
    );

    if (key == 'weight' ||
        key == 'height' ||
        key == 'waist_circumference' ||
        key == 'healthy_food_frequency' ||
        key == 'family_diabetes_history') {
      updatedValues['findrisk_score'] =
          calculateFindRiskScore(tempState).toString();

      updatedValues['risk_classification'] =
          getRiskClassification(int.parse(updatedValues['findrisk_score']));
    }

    emit(state.copyWith(formValues: updatedValues));
  }

  void updateCheckboxValue(String key, bool value) {
    final updatedValues = Map<String, bool>.from(state.checkboxValues);

    if (value) {
      updatedValues.updateAll((k, v) => false);
    }

    updatedValues[key] = value;

    emit(state.copyWith(checkboxValues: updatedValues));
  }

  Future<void> saveForm({bool isFinishing = false}) async {
    final current = state.formData;
    final global = state.globalFormData;

    if (current == null || global == null) return;

    // Aplica os valores preenchidos nos campos da seção atual
    List<FormSection> applyValues(List<FormSection> sections) {
      return sections.map((section) {
        final filledFields = section.fields.map((field) {
          final value = state.formValues[field.name] ??
              state.checkboxValues[field.name] ??
              state.booleanSelections[field.name];
          return field.copyWith(value: value?.toString());
        }).toList();

        return section.copyWith(fields: filledFields);
      }).toList();
    }

    final currentSectionsWithValues = applyValues(current.sections);

    // Substitui as seções equivalentes no globalFormData pelo que foi preenchido
    final updatedSections = global.sections.map((section) {
      final replacement = currentSectionsWithValues.firstWhere(
        (s) => s.title == section.title,
        orElse: () => section,
      );
      return replacement;
    }).toList();

    final updatedGlobal = global.copyWith(
        sections: updatedSections,
        status: isFinishing ? FormStatus.completed : FormStatus.draft);

    emit(state.copyWith(globalFormData: updatedGlobal));

    try {
      final model = SendFormDataModel(
        formId: state.file ?? '',
        formData: updatedGlobal,
        traineeId: studentDetailsBloc.state.student.id,
        responseId: state.responseId,
      );

      bool response;
      if (state.responseId != null) {
        response = await FormService().updateForm(model);
      } else {
        final formId = await FormService().saveForm(model);
        emit(state.copyWith(responseId: formId));
        response = true;
      }
      if (!response) {
        throw Exception('Erro ao salvar formulário');
      }
    } catch (e) {
      throw Exception('Erro ao salvar formulário: $e');
    }
  }

  bool validateForm() {
    if (state.formData == null || state.globalFormData == null) return false;

    final isGlobalValidation =
        state.formData!.title == state.globalFormData!.title;

    final sectionsToValidate = isGlobalValidation
        ? state.globalFormData!.sections
        : state.formData!.sections;

    final birthDate = studentDetailsBloc.state.student.birthDate;
    final isElderly = DateTime.now().difference(birthDate).inDays ~/ 365 >= 60;

    for (final section in sectionsToValidate) {
      // Aplica a regra da idade para ignorar seções que não se aplicam
      if (isElderly && section.title == 'TESTE DE APTIDÃO FÍSICA') {
        continue;
      } else if (!isElderly &&
          section.title == 'TESTE DE APTIDÃO FÍSICA (IDOSOS)') {
        continue;
      }

      final requiredFields = section.requiredFields ?? [];

      for (final requiredFieldName in requiredFields) {
        final field = section.fields.firstWhere(
          (f) => f.name == requiredFieldName,
          orElse: () => FormFieldModel(name: 'not_found', type: 'text'),
        );

        if (field.name == 'not_found') {
          debugPrint('Campo obrigatório não encontrado: $requiredFieldName');
          continue;
        }

        final value = state.formValues[field.name];
        final checkboxValue = state.checkboxValues[field.name];
        final booleanValue = state.booleanSelections[field.name];

        bool isValid = false;

        switch (field.type) {
          case 'textfield':
          case 'calculated':
          case 'select':
            isValid =
                value != null && (value is! String || value.trim().isNotEmpty);
            break;
          case 'checkbox':
            isValid = checkboxValue == true;
            break;
          case 'boolean':
            isValid = booleanValue == 'sim' || booleanValue == 'nao';
            break;
          default:
            break;
        }

        if (!isValid) {
          debugPrint(
              'Campo obrigatório não preenchido: ${field.name} na seção ${section.title}');
          return false;
        }
      }
    }

    return true;
  }

  void updateBooleanSelection(String key, String value) {
    final updatedSelections = Map<String, String>.from(state.booleanSelections);
    updatedSelections[key] = value;
    final updatedValues = Map<String, dynamic>.from(state.formValues);
    updatedValues[key] = value;

    final tempState = state.copyWith(
      booleanSelections: updatedSelections,
      formValues: updatedValues,
    );

    if (key == 'physical_activity_daily' ||
        key == 'regular_medication_for_high_blood_pressure' ||
        key == 'high_blood_glucose_history') {
      updatedValues['findrisk_score'] =
          calculateFindRiskScore(tempState).toString();

      updatedValues['risk_classification'] = getRiskClassification(
        int.parse(updatedValues['findrisk_score']),
      );
    }
    emit(state.copyWith(
      booleanSelections: updatedSelections,
      formValues: updatedValues,
    ));
  }

  void updateDistrictSelection(String district) {
    final formData = state.formData;
    if (formData == null) return;

    // Procura o campo 'unidade_saude' dentro das seções
    FormFieldModel? unidadeSaudeField;
    for (final section in formData.sections) {
      unidadeSaudeField = section.fields.firstWhere(
        (f) => f.name == 'unidade_saude',
        orElse: () => FormFieldModel(name: 'not_found', type: 'text'),
      );
      if (unidadeSaudeField.name == 'unidade_saude') break;
    }

    if (unidadeSaudeField == null ||
        unidadeSaudeField.name != 'unidade_saude') {
      return;
    }

    List<dynamic> newHealthUnitOptions = [];

    if (unidadeSaudeField.options is Map<String, dynamic>) {
      newHealthUnitOptions =
          (unidadeSaudeField.options[district] as List<dynamic>?) ?? [];
    }
    final updatedValues = Map<String, dynamic>.from(state.formValues);
    updatedValues['unidade_saude'] = null;

    emit(state.copyWith(
      selectedDistrict: district,
      healthUnitOptions: newHealthUnitOptions,
      isHealthUnitEnabled: true,
      formValues: updatedValues,
    ));
  }

  void setUser(User value) => emit(state.copyWith(user: value));
}
