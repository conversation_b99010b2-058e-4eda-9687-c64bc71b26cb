{"title": "Informações de Cadastro", "status": "draft", "sections": [{"title": "DADOS PESSOAIS", "fields": [{"key": "email", "type": "text", "label": "E-mail"}, {"key": "data_nascimento", "type": "text", "label": "Data de nascimento"}, {"key": "contato_emergencia", "type": "text", "label": "Contato de emergência"}, {"key": "telefone", "type": "text", "label": "Telefone"}, {"key": "raca", "type": "text", "label": "Raça"}, {"key": "orientacao_sexual", "type": "text", "label": "Orientação Sexual"}, {"key": "identidade_de_genero", "type": "text", "label": "Identidade de Gênero"}, {"key": "endereco", "type": "text", "label": "Endereço"}]}, {"title": "PAR-Q", "description": "Este questionário tem o objetivo de identificar a necessidade de que você seja avaliado por um médico antes do início da atividade física.\n\nPor favor, assinale 'SIM' ou 'NÃO' às seguintes perguntas:", "requiredFields": ["q1", "q2", "q3", "q4", "q5", "q6", "q7", "q8"], "fields": [{"key": "q1", "type": "boolean", "templateOptions": {"label": "1. Algum médico já disse que você possui algum problema de coração e que só deveria realizar atividade física supervisionado por profissionais de saúde?"}}, {"key": "q2", "type": "boolean", "templateOptions": {"label": "2. <PERSON><PERSON><PERSON> sente dores no peito enquanto pratica atividade física?"}}, {"key": "q3", "type": "boolean", "templateOptions": {"label": "3. No último mês, você sentiu dores no peito enquanto praticava atividade física?"}}, {"key": "q4", "type": "boolean", "templateOptions": {"label": "4. Você apresenta desequilíbrio devido à tontura e/ou perda de consciência?"}}, {"key": "q5", "type": "boolean", "templateOptions": {"label": "5. <PERSON><PERSON><PERSON> possui algum problema ósseo ou articular que poderia ser piorado pela atividade física?"}}, {"key": "q6", "type": "boolean", "templateOptions": {"label": "6. Voc<PERSON> toma atualmente algum medicamento para pressão arterial e/ou problema de coração?"}}, {"key": "q7", "type": "boolean", "templateOptions": {"label": "7. <PERSON><PERSON><PERSON> possui alguma outra condição médica ou problema de saúde que não foi mencionado acima?"}}, {"key": "q8", "type": "boolean", "templateOptions": {"label": "8. <PERSON><PERSON> relatado."}}]}, {"title": "DCNT e outras condições de saúde", "description": "Por favor, assinale “SIM” ou “NÃO” às seguintes perguntas:", "requiredFields": ["hipert<PERSON><PERSON>", "diabetes", "cardiopatia", "infarto", "avc", "cancer", "dislipidemia", "renal", "asma", "dpoc", "saude_mental", "nao_relatado"], "fields": [{"key": "hipert<PERSON><PERSON>", "type": "boolean", "templateOptions": {"label": "1. Hipertensão arterial"}}, {"key": "diabetes", "type": "boolean", "templateOptions": {"label": "2. Diabe<PERSON>"}}, {"key": "cardiopatia", "type": "boolean", "templateOptions": {"label": "3. Cardiopatia"}}, {"key": "infarto", "type": "boolean", "templateOptions": {"label": "4. <PERSON><PERSON>"}}, {"key": "avc", "type": "boolean", "templateOptions": {"label": "5. <PERSON><PERSON>/Derrame"}}, {"key": "cancer", "type": "boolean", "templateOptions": {"label": "6. <PERSON><PERSON> ou teve c<PERSON><PERSON>"}}, {"key": "dislipidemia", "type": "boolean", "templateOptions": {"label": "7. <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}}, {"key": "renal", "type": "boolean", "templateOptions": {"label": "8. <PERSON><PERSON><PERSON> renal"}}, {"key": "asma", "type": "boolean", "templateOptions": {"label": "9. <PERSON><PERSON>"}}, {"key": "dpoc", "type": "boolean", "templateOptions": {"label": "10. DPOC"}}, {"key": "saude_mental", "type": "boolean", "templateOptions": {"label": "11. <PERSON><PERSON> di<PERSON>n<PERSON><PERSON> de algum problema de saúde mental"}}, {"key": "nao_relatado", "type": "boolean", "templateOptions": {"label": "12. <PERSON><PERSON> relatado."}}]}, {"title": "Informações do Polo", "description": "Por favor, selecione a que melhor atende:", "requiredFields": ["polo", "distrito_sanitario", "cns_polo", "turno"], "fields": [{"key": "polo", "type": "select", "templateOptions": {"label": "Polo *", "options": []}}, {"key": "distrito_sanitario", "type": "select", "templateOptions": {"label": "Distrito sanitário *", "canEdit": false, "options": [{"label": "Distrito Sanitário I", "value": "1"}, {"label": "Distrito Sanitário II", "value": "2"}, {"label": "Distrito Sanitário III", "value": "3"}, {"label": "Distrito Sanitário IV", "value": "4"}, {"label": "Distrito Sanitário V", "value": "5"}, {"label": "Distrito Sanitário VI", "value": "6"}, {"label": "Distrito Sanitário VII", "value": "7"}, {"label": "Distrito Sanitário VIII", "value": "8"}]}}, {"key": "cns_polo", "type": "textfield", "templateOptions": {"label": "CNES do polo *", "canEdit": false}}, {"key": "turno", "type": "select", "templateOptions": {"label": "Turno *", "options": [{"label": "Manhã", "value": "manha"}, {"label": "Noite", "value": "noite"}]}}]}, {"title": "Informações de Saúde", "description": "Por favor, selecione a que melhor atende:", "requiredFields": ["tem_deficiencia", "distrito_residencia", "plano_saude"], "fields": [{"key": "tem_deficiencia", "type": "select", "templateOptions": {"label": "Tem alguma deficiência? *", "options": [{"label": "Não", "value": "nao"}, {"label": "Auditiva", "value": "auditiva"}, {"label": "Visual", "value": "visual"}, {"label": "Intelectual/Cognitiva", "value": "intelectual"}, {"label": "Física", "value": "fisi<PERSON>"}, {"label": "Outro", "value": "outro"}]}}, {"key": "cns", "type": "textfield", "templateOptions": {"label": "Cartão Nacional do SUS"}}, {"key": "distrito_residencia", "type": "select", "templateOptions": {"label": "Distrito de residência *", "options": [{"label": "Distrito Sanitário I", "value": "1"}, {"label": "Distrito Sanitário II", "value": "2"}, {"label": "Distrito Sanitário III", "value": "3"}, {"label": "Distrito Sanitário IV", "value": "4"}, {"label": "Distrito Sanitário V", "value": "5"}, {"label": "Distrito Sanitário VI", "value": "6"}, {"label": "Distrito Sanitário VII", "value": "7"}, {"label": "Distrito Sanitário VIII", "value": "8"}]}}, {"key": "unidade_saude", "type": "select", "templateOptions": {"label": "Unidade de saúde", "disabled": true, "dependsOn": "distrito_residencia", "options": {"1": [{"label": "US 123 CS PROF CESAR MONTEZUMA", "value": "123"}, {"label": "US 218 USF COQUE", "value": "218"}, {"label": "US 232 USF ILHA SANTA TEREZINHA", "value": "232"}, {"label": "US 240 USF COELHOS I", "value": "240"}, {"label": "US 241 USF COELHOS II", "value": "241"}, {"label": "US 242 USF SANTO AMARO I SITIO DO CEU", "value": "242"}, {"label": "US 243 USF SANTO AMARO II", "value": "243"}, {"label": "US 278 USF NOSSA SRA DO PILAR BAIRRO DO RECIFE", "value": "278"}, {"label": "US 285 USF SAO JOSE DO COQUE", "value": "285"}, {"label": "US 334 USF CABANGA", "value": "334"}, {"label": "US 404 USF SANTO AMARO III", "value": "404"}], "2": [{"label": "US 138 USF DR LUIZ WILSOM", "value": "138"}, {"label": "US 155 CS PROF MONTEIRO DE MORAIS/ALTO DOS COQUEIROS", "value": "155"}, {"label": "US 179 USF ALTO DO CEU", "value": "179"}, {"label": "US 221 USF ILHA DE JOANEIRO", "value": "221"}, {"label": "US 222 USF CORREGO DO CURIO", "value": "222"}, {"label": "US 226 USF CHAO DE ESTRELAS", "value": "226"}, {"label": "US 244 USF PROF ANTONIO FRANCISCO AREIAS", "value": "244"}, {"label": "US 262 USF JOSE SEVERIANO DA SILVA", "value": "262"}, {"label": "US 273 USF BIANOR TEODOSIO", "value": "273"}, {"label": "US 274 USF TIA REGINA", "value": "274"}, {"label": "US 276 USF UPINHA DIA ALTO DO PASCOAL", "value": "276"}, {"label": "US 286 USF IRMA TEREZINHA I E II", "value": "286"}, {"label": "US 291 USF ALTO DOS COQUEIROS CORREGO DA JAQUEIRA", "value": "291"}, {"label": "US 302 USF BYRON SARINHO", "value": "302"}, {"label": "US 309 USF PONTO DE PARADA", "value": "309"}, {"label": "US 327 USF CLUBE DOS DELEGADOS", "value": "327"}, {"label": "US 328 USF ALTO DO MARACANA", "value": "328"}, {"label": "US 339 USF ALTO DO CAPITAO", "value": "339"}, {"label": "US 395 USF UPINHA DIA DRA FERNANDA WANDERLEY", "value": "395"}, {"label": "US 401 USF UPINHA DIA GOVERNADOR EDUARDO CAMPOS", "value": "401"}, {"label": "US 403 USF UPINHA DIA CHIE I E II", "value": "403"}], "3": [{"label": "US 103 CS PROF MÁRIO RAMOS", "value": "103"}, {"label": "US 109 CS FRANCISCO PIGNATARI", "value": "109"}, {"label": "US 171 USF JOAQUIM COSTA CARVALHO", "value": "171"}, {"label": "US 182 UPINHA USF PADRE JOSE EDWALDO GOMES", "value": "182"}, {"label": "US 216 USF APIPUCOS", "value": "216"}, {"label": "US 258 USF SITIO DOS PINTOS", "value": "258"}, {"label": "US 259 USF SITIO SAO BRAZ", "value": "259"}, {"label": "US 260 USF CORREGO DA FORTUNA", "value": "260"}, {"label": "US 336 USF UNIAO DAS VILAS", "value": "336"}], "4": [{"label": "US 106 CS PROF JOAQUIM CAVALCANTE", "value": "106"}, {"label": "US 112 CS DR JOSE DUSTAN CARVALHO SOARES", "value": "112"}, {"label": "US 149 CS OLINTO OLIVEIRA", "value": "149"}, {"label": "US 184 USF VILA UNIAO", "value": "184"}, {"label": "US 224 USF CARANGUEIJO", "value": "224"}, {"label": "US 225 USF SKYLAB II", "value": "225"}, {"label": "US 233 USF VIETNA", "value": "233"}, {"label": "US 234 USF RODA DE FOGO COSIROF", "value": "234"}, {"label": "US 235 USF RODA DE FOGO SINOS", "value": "235"}, {"label": "US 236 USF RODA DE FOGO MACAE", "value": "236"}, {"label": "US 237 USF SITIO DAS PALMEIRAS", "value": "237"}, {"label": "US 247 USF ROSA SELVAGEM", "value": "247"}, {"label": "US 248 USF BARREIRAS", "value": "248"}, {"label": "US 252 USF ENGENHO DO MEIO", "value": "252"}, {"label": "US 254 USF BRASILIT", "value": "254"}, {"label": "US 255 USF UPINHA 24H VILA ARRAES", "value": "255"}, {"label": "US 280 USF SITIO CARDOSO", "value": "280"}, {"label": "US 295 USF COSME E DAMIAO", "value": "295"}, {"label": "US 331 USF PROF AMAURY DE MEDEIROS", "value": "331"}, {"label": "US 337 USF SITIO WANDERLEY", "value": "337"}, {"label": "US 349 USF CASARAO DO CORDEIRO", "value": "349"}, {"label": "US 378 USF JARDIM TERESOPOLIS", "value": "378"}, {"label": "US 442 USF UPINHA DIA SANTA LUZIA EMOCY KRAUSE", "value": "442"}], "5": [{"label": "US 117 CS GASPAR REGUEIRA COSTA", "value": "117"}, {"label": "US 142 CS BIDU KRAUSE", "value": "142"}, {"label": "US 150 CS PROFESSOR FERNANDES FIGUEIRA", "value": "150"}, {"label": "US 158 CS PAM CEASA", "value": "158"}, {"label": "US 161 CS PROF ROMERO MARQUES", "value": "161"}, {"label": "US 177 USF CHICO MENDES/XIMBORÉ", "value": "177"}, {"label": "US 186 USF JARDIM UCHOA", "value": "186"}, {"label": "US 238 USF IRAQUE", "value": "238"}, {"label": "US 239 USF COQUEIRAL I E II", "value": "239"}, {"label": "US 245 USF PLANETA DOS MACACOS II", "value": "245"}, {"label": "US 265 USF MANGUEIRA I", "value": "265"}, {"label": "US 266 USF MANGUEIRA II", "value": "266"}, {"label": "US 284 USF VILA SAO MIGUEL MARROM GLACE", "value": "284"}, {"label": "US 294 USF VILA TAMANDARE BEIRINHA", "value": "294"}, {"label": "US 300 USF DR GERALDO BARRETO CAMPELO SAN MARTIN", "value": "300"}, {"label": "US 301 USF BONGI BOA IDEIA", "value": "301"}, {"label": "US 323 USF MUSTARDINHA", "value": "323"}, {"label": "US 338 USF UPINHA DIA JARDIM SAO PAULO", "value": "338"}, {"label": "US 344 USF JIQUIA I E II", "value": "344"}, {"label": "US 345 USF PLANETA DOS MACACOS I", "value": "345"}, {"label": "US 393 USF UPINHA DIA BONGI NOVO PRADO", "value": "393"}, {"label": "US 399 USF UPINHA DIA NOVO JIQUIA", "value": "399"}], "6": [{"label": "US 119 CS PROF JOSÉ CARNEIRO LEÃO", "value": "119"}, {"label": "US 126 CS VER ROMILDO GOMES", "value": "126"}, {"label": "US 137 CS PROF DJAIR BRINDEIRO", "value": "137"}, {"label": "US 148 CS DOM MIGUEL DE LIMA VALVERDE", "value": "148"}, {"label": "US 173 USF DANCING DAYS", "value": "173"}, {"label": "US 174 USF SITIO GRANDE", "value": "174"}, {"label": "US 187 USF ILHA DE DEUS", "value": "187"}, {"label": "US 268 USF CAFESOPOLIS", "value": "268"}, {"label": "US 269 USF BEIRA DO RIO / COMUNIDADE BOA VIAGEM", "value": "269"}, {"label": "US 292 USF VILA DO IPSEP", "value": "292"}, {"label": "US 296 USF COQUEIRAL IMBIRIBEIRA", "value": "296"}, {"label": "US 297 USF DO PINA", "value": "297"}, {"label": "US 307 USF DR GUILHERME J ROBALINHO DE OLIVEIRA CAVALCANTI / ENCANTA MOCA", "value": "307"}, {"label": "US 316 USF BERNARD VAN LEER", "value": "316"}, {"label": "US 326 USF JADER DE ANDRADE COMUNIDADE ENTRA APULSO", "value": "326"}, {"label": "US 342 USF DJALMA HOLANDA CAVALCANTE", "value": "342"}], "7": [{"label": "US 120 CS MÁRIO MONTEIRO MELO", "value": "120"}, {"label": "US 121 CS PROF BRUNO MAIA", "value": "121"}, {"label": "US 152 CS INÁ ROSA BORGES", "value": "152"}, {"label": "US 175 USF DR DIOGENES CAVALCANTI", "value": "175"}, {"label": "US 183 USF SITIO DOS MACACOS", "value": "183"}, {"label": "US 231 USF CORREGO DA BICA", "value": "231"}, {"label": "US 251 USF DA GUABIRABA", "value": "251"}, {"label": "US 256 USF PASSARINHO BAIXO", "value": "256"}, {"label": "US 257 USF GILBERTO FREIRE/BOLA NA REDE", "value": "257"}, {"label": "US 261 USF ALTO DO EUCALIPTO", "value": "261"}, {"label": "US 272 USF SANTA TEREZA", "value": "272"}, {"label": "US 279 USF PASSARINHO ALTO", "value": "279"}, {"label": "US 283 USF VILA BOA VISTA", "value": "283"}, {"label": "US 287 USF ALTO JOSE DO PINHO/IRMÃ DENISE", "value": "287"}, {"label": "US 288 USF MORRO DA CONCEICAO", "value": "288"}, {"label": "US 290 USF DA MANGABEIRA", "value": "290"}, {"label": "US 305 USF DA MACAXEIRA", "value": "305"}, {"label": "US 324 USF ALTO JOSE BONIFACIO / ALCIDES CODECEIRA", "value": "324"}, {"label": "US 350 USF CORREGO DO EUCALIPTO", "value": "350"}, {"label": "US 352 USF UPINHA 24H DR HELIO MENDONCA COR DO JENIPAPO", "value": "352"}, {"label": "US 394 USF UPINHA 24H DR MOACYR ANDRE GOMES", "value": "394"}, {"label": "US 397 USF UPINHA DIA CORREGO DO EUCLIDES", "value": "397"}, {"label": "US 400 USF UPINHA DIA DOM HELDER", "value": "400"}], "8": [{"label": "US 104 CS SEBASTIÃO IVO RABELO", "value": "104"}, {"label": "US 113 CS DR ARISTARCHO DOURADO DE AZEVEDO", "value": "113"}, {"label": "US 154 USF + RIO PAJEÚ", "value": "154"}, {"label": "US 172 USF TRES CARNEIROS", "value": "172"}, {"label": "US 228 USF UPINHA DIA DES JOSE MANOEL DE FREITAS UR 4UR 5", "value": "228"}, {"label": "US 229 USF UR 10", "value": "229"}, {"label": "US 230 USF LAGOA ENCANTADA", "value": "230"}, {"label": "US 250 USF UR12 UR5 3 ETAPA", "value": "250"}, {"label": "US 267 USF UR 2", "value": "267"}, {"label": "US 270 USF MONTE VERDE", "value": "270"}, {"label": "US 281 USF VILA DOS MILAGRES", "value": "281"}, {"label": "US 282 USF VILA DAS AEROMOCAS", "value": "282"}, {"label": "US 289 USF JOSUE DE CASTRO", "value": "289"}, {"label": "US 298 USF JORDAO ALTO", "value": "298"}, {"label": "US 299 USF JORDAO BAIXO", "value": "299"}, {"label": "US 311 USF AGUA VIVA", "value": "311"}, {"label": "US 312 USF VILA DO SESI", "value": "312"}, {"label": "US 313 USF TRES CARNEIROS DE BAIXO ZUMBI DO PACHECO", "value": "313"}, {"label": "US 314 USF RIO DA PRATA", "value": "314"}, {"label": "US 315 USF UR 3", "value": "315"}, {"label": "US 317 USF ALTO DA BELA VISTA", "value": "317"}, {"label": "US 341 USF PROF FERNANDO FIGUEIRA / PANTANAL", "value": "341"}, {"label": "US 346 USF ALTO DA JAQUEIRA", "value": "346"}, {"label": "US 347 USF PARQUE DO MILAGRE", "value": "347"}, {"label": "US 351 USF PAZ E AMOR", "value": "351"}, {"label": "US 373 USF CIDADE OPERARIA", "value": "373"}]}}}, {"key": "plano_saude", "type": "boolean", "templateOptions": {"label": "Possui plano de saúde privado *"}}]}, {"title": "Informações Socioeconômicas", "description": "Por favor, selecione a que melhor atende:", "requiredFields": ["frequenta_escola", "escolaridade", "ocupacao", "situacao_mercado_trabalho", "situacao_rua"], "fields": [{"key": "frequenta_escola", "type": "boolean", "templateOptions": {"label": "Frequenta a escola *"}}, {"key": "escolaridade", "type": "select", "templateOptions": {"label": "Curso mais elevado que frequenta ou frequent<PERSON> *", "options": [{"label": "Creche", "value": "creche"}, {"label": "Pré-escola (Exceto CA)", "value": "pre_escola"}, {"label": "Classe de Alfabetização – CA", "value": "ca"}, {"label": "Ensino fundamental 1º a 4º séries", "value": "fundamental_1_4"}, {"label": "Ensino fundamental 5º a 8º séries", "value": "fundamental_5_8"}, {"label": "Ensino fundamental Completo", "value": "fundamental_completo"}, {"label": "Ensino fundamental especial", "value": "fundamental_especial"}, {"label": "Ensino fundamental EJA – séries iniciais (Supletivo 1º a 4º)", "value": "eja_iniciais"}, {"label": "Ensino fundamental EJA – séries iniciais (Supletivo 5º a 8º)", "value": "eja_finais"}, {"label": "<PERSON><PERSON><PERSON> médio, médio 2º ciclo (Cientifico, técnico etc.)", "value": "medio_completo"}, {"label": "Ensino médio especial", "value": "medio_especial"}, {"label": "Ensino médio EJA (supletivo)", "value": "medio_eja"}, {"label": "Superior, aperfeiçoamento, especialização, mestrado, doutorado", "value": "superior"}, {"label": "Alfabetização para adultos (Mobral, etc.)", "value": "alfabetizacao_adultos"}, {"label": "<PERSON><PERSON><PERSON>", "value": "<PERSON><PERSON><PERSON>"}]}}, {"key": "ocupacao", "type": "textfield", "templateOptions": {"label": "Ocupação *"}}, {"key": "situacao_mercado_trabalho", "type": "select", "templateOptions": {"label": "Situação no mercado de trabalho *", "options": [{"label": "Empre<PERSON><PERSON>", "value": "empregador"}, {"label": "Assalariado com carteira de trabalho", "value": "assalariado_ctps"}, {"label": "Assalariado sem carteira de trabalho", "value": "assalariado_sem_ctps"}, {"label": "Autônomo com previdência social", "value": "autonomo_com_previdencia"}, {"label": "Autônomo sem previdência social", "value": "autonomo_sem_previdencia"}, {"label": "Aposentado/pensionista", "value": "aposentado"}, {"label": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "value": "desempre<PERSON>"}, {"label": "<PERSON>ão trabalha", "value": "nao_trabalha"}, {"label": "Ser<PERSON><PERSON>/Mi<PERSON>ar", "value": "servidor_publico"}, {"label": "Outro", "value": "outro"}]}}, {"key": "situacao_rua", "type": "boolean", "templateOptions": {"label": "Está em situação de rua? *"}}]}, {"title": "TIPO DE ATENDIMENTO", "description": "Por favor, selecione a que melhor atende:", "requiredFields": ["tipo_atendimento", "professional_name"], "fields": [{"key": "tipo_atendimento", "type": "select", "templateOptions": {"label": "Selecione o tipo de atendimento *", "options": [{"label": "Escuta inicial/Orientação", "value": "escuta_inicial"}, {"label": "Consulta do dia", "value": "consulta_dia"}, {"label": "Atendimento de urgência", "value": "urgencia"}, {"label": "Consulta agendada", "value": "<PERSON>da"}]}}, {"key": "professional_name", "type": "select", "templateOptions": {"label": "Nome do profissional *", "options": []}}, {"key": "professional_cns", "type": "textfield", "templateOptions": {"label": "CNS do profissional *", "canEdit": false}}]}]}