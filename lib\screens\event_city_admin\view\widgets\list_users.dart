import 'package:city_academy/core/util/global_instances.dart';
import 'package:city_academy/core/widgets/custom_checkbox.dart';
import 'package:city_academy/core/widgets/custom_divider.dart';
import 'package:city_academy/routes/app_routes.dart';
import 'package:city_academy/routes/routes.dart';
import 'package:city_academy/screens/student_details/model/user_in_activity.dart';
import 'package:city_academy/theme/colors.dart';
import 'package:city_academy/theme/texts.dart';
import 'package:flutter/material.dart';

class ListUserCheckIn extends StatelessWidget {
  final List<UserInActivity> usersCheckIn;
  final List<String> listSelected;
  final bool showTrailing;

  const ListUserCheckIn({
    super.key,
    required this.usersCheckIn,
    required this.listSelected,
    this.showTrailing = true,
  });

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'Solicitações',
                style: titleText16.copyWith(color: CustomColors.neutralDark700),
              ),
              Text(
                'Compareceram',
                style: titleText12.copyWith(color: CustomColors.neutralDark700),
              )
            ],
          ),
          const SizedBox(height: 16),
          if (usersCheckIn.isEmpty)
            SizedBox(
                height: 300,
                child: Center(
                    child: Text(
                  'Sem usuários para confirmar presença',
                  style: titleText14.copyWith(
                    fontWeight: FontWeight.w500,
                    color: CustomColors.neutralBlack,
                  ),
                )))
          else
            ...usersCheckIn.map(
              (user) {
                return Column(
                  children: [
                    const CustomDivider(),
                    ListTile(
                      onTap: () async {
                        studentDetailsBloc.setUser(user);
                        router.push(Routes.studentDetails);
                      },
                      leading: CircleAvatar(
                        backgroundColor: (user.diseases ?? {}).isNotEmpty
                            ? CustomColors.warningYellow
                            : user.isComplete ?? false
                                ? CustomColors.greenBase
                                : CustomColors.errorRed,
                        child: const Icon(
                          Icons.person,
                          color: Colors.white,
                        ),
                      ),
                      title: Text(
                        user.name,
                        style: titleText16.copyWith(
                            color: CustomColors.neutralDark700),
                      ),
                      trailing: showTrailing
                          ? CustomCheckbox(
                              value: listSelected.contains(user.id),
                              onChanged: (newValue) {
                                if (newValue == true) {
                                  eventCityAdminCubit
                                      .setUserInConfirmation(user.id);
                                } else {
                                  eventCityAdminCubit
                                      .removeUserInConfirmation(user.id);
                                }
                              })
                          : null,
                    ),
                  ],
                );
              },
            )
        ],
      ),
    );
  }
}
