import 'package:city_academy/core/widgets/buttons/info_row_button.dart';
import 'package:city_academy/theme/colors.dart';
import 'package:flutter/material.dart';

class CardButton extends StatelessWidget {
  final String? leadingIcon;
  final Color? leadingIconColor;
  final String title;
  final String? subTitle;
  final Function()? onTap;

  const CardButton({
    super.key,
    this.leadingIcon,
    this.leadingIconColor,
    required this.title,
    this.subTitle,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 6,
      color: CustomColors.neutralWhite,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      child: Container(
        decoration: BoxDecoration(
          border: Border.all(color: CustomColors.neutralGray200),
          borderRadius: BorderRadius.circular(16),
        ),
        child: InfoRowButton(
          onTap: onTap,
          leadingIcon: leadingIcon,
          title: title,
          leadingIconColor: leadingIconColor,
          subTitle: subTitle,
        ),
      ),
    );
  }
}
