import 'package:city_academy/screens/activity_list/model/activity_model.dart';
import 'package:city_academy/screens/login/model/coach.dart';
import 'package:copy_with_extension/copy_with_extension.dart';
import 'package:empty_annotation/empty_annotation.dart';

part 'event_city_admin_model.g.dart';

@Empty()
@CopyWith()
class EventCityAdminModel extends ActivityModel {
  final Coach? coach;
  final String type;
  final DateTime activityTime;
  final bool active;
  final String duration;
  final int maxQtd;
  final int minQtd;
  final String status;
  final String recurrenceId;
  final int numParticipantes;
  final int numAvaliacoesAlteradas;
  final int atividadeTipo;
  final List<int> temasParaReuniao;
  final List<int> temasParaSaude;
  final List<int> praticasEmSaude;
  final List<int> publicoAlvo;
  final int turno;
  final bool pseEducacao;
  final bool pseSaude;
  final int inep;
  final String id;
  final String outraLocalidade;
  final String cnesLocalAtividade;
  final List<String> users;
  final String coachId;
  final List<String> coachs;
  final DateTime createdAt;
  final DateTime updatedAt;

  EventCityAdminModel({
    required super.activityId,
    required super.activityCenterId,
    required super.startTime,
    required super.endTime,
    required super.description,
    required super.eventName,
    required this.coach,
    required this.type,
    required this.activityTime,
    required this.active,
    required this.duration,
    required this.maxQtd,
    required this.minQtd,
    required this.status,
    required this.recurrenceId,
    required this.numParticipantes,
    required this.numAvaliacoesAlteradas,
    required this.atividadeTipo,
    required this.temasParaReuniao,
    required this.temasParaSaude,
    required this.praticasEmSaude,
    required this.publicoAlvo,
    required this.turno,
    required this.pseEducacao,
    required this.pseSaude,
    required this.inep,
    required this.id,
    required this.outraLocalidade,
    required this.cnesLocalAtividade,
    required this.users,
    required this.coachId,
    required this.coachs,
    required this.createdAt,
    required this.updatedAt,
  });

  factory EventCityAdminModel.fromJson(Map<String, dynamic> json) {
    return EventCityAdminModel(
      activityId: json['id'],
      activityCenterId: json['activity_center_id'],
      startTime: DateTime.parse(json['start_time']),
      endTime: DateTime.parse(json['end_time']),
      description: json['description'] ?? '',
      eventName: json['event_name'] ?? '',
      coach: json['coach'] != null ? Coach.fromJson(json['coach']) : null,
      type: json['type'] ?? '',
      activityTime: DateTime.parse(json['activity_time']),
      active: json['active'] ?? false,
      duration: json['duration'] ?? '',
      maxQtd: json['max_qtd'] ?? 0,
      minQtd: json['min_qtd'] ?? 0,
      status: json['status'] ?? '',
      recurrenceId: json['recurrence_id'] ?? '',
      numParticipantes: json['num_participantes'] ?? 0,
      numAvaliacoesAlteradas: json['num_avaliacoes_alteradas'] ?? 0,
      atividadeTipo: json['atividade_tipo'] ?? 0,
      temasParaReuniao: List<int>.from(json['temas_para_reuniao'] ?? []),
      temasParaSaude: List<int>.from(json['temas_para_saude'] ?? []),
      praticasEmSaude: List<int>.from(json['praticas_em_saude'] ?? []),
      publicoAlvo: List<int>.from(json['publico_alvo'] ?? []),
      turno: json['turno'] ?? 0,
      pseEducacao: json['pse_educacao'] ?? false,
      pseSaude: json['pse_saude'] ?? false,
      inep: json['inep'] ?? 0,
      id: json['id'] ?? '',
      outraLocalidade: json['outra_localidade'] ?? '',
      cnesLocalAtividade: json['cnes_local_atividade'] ?? '',
      users: List<String>.from(json['users'] ?? []),
      coachId: json['coach_id'] ?? '',
      coachs: List<String>.from(json['coachs'] ?? []),
      createdAt: DateTime.parse(json['created_at']),
      updatedAt: DateTime.parse(json['updated_at']),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'type': type,
      'activity_center_id': activityCenterId,
      'activity_time': activityTime.toIso8601String(),
      'active': active,
      'duration': duration,
      'start_time': startTime.toIso8601String(),
      'end_time': endTime.toIso8601String(),
      'max_qtd': maxQtd,
      'min_qtd': minQtd,
      'description': description,
      'event_name': eventName,
      'status': status,
      'recurrence_id': recurrenceId,
      'num_participantes': numParticipantes,
      'num_avaliacoes_alteradas': numAvaliacoesAlteradas,
      'atividade_tipo': atividadeTipo,
      'temas_para_reuniao': temasParaReuniao,
      'temas_para_saude': temasParaSaude,
      'praticas_em_saude': praticasEmSaude,
      'publico_alvo': publicoAlvo,
      'turno': turno,
      'pse_educacao': pseEducacao,
      'pse_saude': pseSaude,
      'inep': inep,
      'id': id,
      'outra_localidade': outraLocalidade,
      'cnes_local_atividade': cnesLocalAtividade,
      'users': users,
      'coach_id': coachId,
      'coachs': coachs,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }
}
