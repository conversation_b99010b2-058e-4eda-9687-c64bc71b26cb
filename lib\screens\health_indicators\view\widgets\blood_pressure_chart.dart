import 'package:city_academy/screens/health_indicators/view/widgets/indicator.dart';
import 'package:fl_chart/fl_chart.dart';
import 'package:flutter/material.dart';

class BloodPressureChart extends StatelessWidget {
  const BloodPressureChart({super.key});

  @override
  Widget build(BuildContext context) {
    final systolic = [
      120,
      122,
      118,
      124,
      119,
      123,
      121,
      120,
      122,
      118,
      119,
      120
    ];
    final diastolic = [80, 82, 78, 79, 77, 81, 80, 79, 80, 78, 77, 80];

    return Card(
      elevation: 6,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text('Pressão arterial',
                style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold)),
            const SizedBox(height: 16),
            const Row(
              children: [
                Indicator(color: Colors.blue, text: '<PERSON>st<PERSON><PERSON>'),
                Sized<PERSON>ox(width: 16),
                Indicator(color: Colors.green, text: 'Diast<PERSON><PERSON>'),
              ],
            ),
            const SizedBox(height: 16),
            <PERSON><PERSON><PERSON><PERSON>(
              height: 200,
              child: LineChart(
                LineChartData(
                  minY: 0,
                  borderData: FlBorderData(show: false),
                  gridData: const FlGridData(show: true),
                  titlesData: const FlTitlesData(
                    leftTitles: AxisTitles(
                      axisNameWidget: Text('Valor mm/Hg'),
                      sideTitles:
                          SideTitles(showTitles: true, reservedSize: 40),
                    ),
                    bottomTitles: AxisTitles(
                      axisNameWidget: Text('Data'),
                      sideTitles: SideTitles(showTitles: true),
                    ),
                  ),
                  lineBarsData: [
                    LineChartBarData(
                      spots: List.generate(
                          systolic.length,
                          (i) =>
                              FlSpot(i.toDouble() + 1, systolic[i].toDouble())),
                      isCurved: true,
                      color: Colors.blue,
                      dotData: const FlDotData(show: false),
                    ),
                    LineChartBarData(
                      spots: List.generate(
                          diastolic.length,
                          (i) => FlSpot(
                              i.toDouble() + 1, diastolic[i].toDouble())),
                      isCurved: true,
                      color: Colors.green,
                      dotData: const FlDotData(show: false),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
