# Generated code do not commit.
file(TO_CMAKE_PATH "B:\\lib\\flutter" FLUTTER_ROOT)
file(TO_CMAKE_PATH "G:\\ProgramasDart\\city_academy" PROJECT_DIR)

set(FLUTTER_VERSION "1.0.0+1" PARENT_SCOPE)
set(FLUTTER_VERSION_MAJOR 1 PARENT_SCOPE)
set(FLUTTER_VERSION_MINOR 0 PARENT_SCOPE)
set(FLUTTER_VERSION_PATCH 0 PARENT_SCOPE)
set(FLUTTER_VERSION_BUILD 1 PARENT_SCOPE)

# Environment variables to pass to tool_backend.sh
list(APPEND FLUTTER_TOOL_ENVIRONMENT
  "FLUTTER_ROOT=B:\\lib\\flutter"
  "PROJECT_DIR=G:\\ProgramasDart\\city_academy"
  "FLUTTER_ROOT=B:\\lib\\flutter"
  "FLUTTER_EPHEMERAL_DIR=G:\\ProgramasDart\\city_academy\\windows\\flutter\\ephemeral"
  "PROJECT_DIR=G:\\ProgramasDart\\city_academy"
  "FLUTTER_TARGET=G:\\ProgramasDart\\city_academy\\lib\\main.dart"
  "DART_OBFUSCATION=false"
  "TRACK_WIDGET_CREATION=true"
  "TREE_SHAKE_ICONS=false"
  "PACKAGE_CONFIG=G:\\ProgramasDart\\city_academy\\.dart_tool\\package_config.json"
)
