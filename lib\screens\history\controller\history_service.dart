import 'package:city_academy/core/shared/base_service.dart';
import 'package:city_academy/screens/history/controller/history_repository.dart';
import 'package:city_academy/screens/history/model/response_form_data.dart';

class HistoryService extends BaseService {
  final _repository = HistoryRepository();

  Future<List<ResponseFormData>> fetchHistory(String formId, String? userId) {
    return super.call(() => _repository.fetchHistory(formId, userId));
  }
}
