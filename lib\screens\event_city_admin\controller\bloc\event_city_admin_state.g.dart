// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'event_city_admin_state.dart';

// **************************************************************************
// CopyWithGenerator
// **************************************************************************

abstract class _$EventCityAdminStateCWProxy {
  /// This function **does support** nullification of nullable fields. All `null` values passed to `non-nullable` fields will be ignored.
  ///
  /// Usage
  /// ```dart
  /// EventCityAdminState(...).copyWith(id: 12, name: "My name")
  /// ````
  EventCityAdminState call({
    bool loading,
    String error,
    String message,
    List<String> listSelected,
    EventCityAdminModel eventCity,
    String activityId,
    List<UserInActivity> users,
    EventCityAdminStatus status,
  });
}

/// Proxy class for `copyWith` functionality. This is a callable class and can be used as follows: `instanceOfEventCityAdminState.copyWith(...)`.
class _$EventCityAdminStateCWProxyImpl implements _$EventCityAdminStateCWProxy {
  const _$EventCityAdminStateCWProxyImpl(this._value);

  final EventCityAdminState _value;

  @override

  /// This function **does support** nullification of nullable fields. All `null` values passed to `non-nullable` fields will be ignored.
  ///
  /// Usage
  /// ```dart
  /// EventCityAdminState(...).copyWith(id: 12, name: "My name")
  /// ````
  EventCityAdminState call({
    Object? loading = const $CopyWithPlaceholder(),
    Object? error = const $CopyWithPlaceholder(),
    Object? message = const $CopyWithPlaceholder(),
    Object? listSelected = const $CopyWithPlaceholder(),
    Object? eventCity = const $CopyWithPlaceholder(),
    Object? activityId = const $CopyWithPlaceholder(),
    Object? users = const $CopyWithPlaceholder(),
    Object? status = const $CopyWithPlaceholder(),
  }) {
    return EventCityAdminState(
      loading: loading == const $CopyWithPlaceholder()
          ? _value.loading
          // ignore: cast_nullable_to_non_nullable
          : loading as bool,
      error: error == const $CopyWithPlaceholder()
          ? _value.error
          // ignore: cast_nullable_to_non_nullable
          : error as String,
      message: message == const $CopyWithPlaceholder()
          ? _value.message
          // ignore: cast_nullable_to_non_nullable
          : message as String,
      listSelected: listSelected == const $CopyWithPlaceholder()
          ? _value.listSelected
          // ignore: cast_nullable_to_non_nullable
          : listSelected as List<String>,
      eventCity: eventCity == const $CopyWithPlaceholder()
          ? _value.eventCity
          // ignore: cast_nullable_to_non_nullable
          : eventCity as EventCityAdminModel,
      activityId: activityId == const $CopyWithPlaceholder()
          ? _value.activityId
          // ignore: cast_nullable_to_non_nullable
          : activityId as String,
      users: users == const $CopyWithPlaceholder()
          ? _value.users
          // ignore: cast_nullable_to_non_nullable
          : users as List<UserInActivity>,
      status: status == const $CopyWithPlaceholder()
          ? _value.status
          // ignore: cast_nullable_to_non_nullable
          : status as EventCityAdminStatus,
    );
  }
}

extension $EventCityAdminStateCopyWith on EventCityAdminState {
  /// Returns a callable class that can be used as follows: `instanceOfEventCityAdminState.copyWith(...)`.
  // ignore: library_private_types_in_public_api
  _$EventCityAdminStateCWProxy get copyWith =>
      _$EventCityAdminStateCWProxyImpl(this);
}

// **************************************************************************
// EmptyGenerator
// **************************************************************************

class EventCityAdminStateEmpty extends EventCityAdminState {
  EventCityAdminStateEmpty()
      : super(
            listSelected: const [],
            eventCity: EventCityAdminModelEmpty(),
            activityId: '',
            users: const [],
            status: EventCityAdminStatus.values.first,
            loading: false,
            error: '',
            message: '');
}
