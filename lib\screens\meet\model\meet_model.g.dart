// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'meet_model.dart';

// **************************************************************************
// CopyWithGenerator
// **************************************************************************

abstract class _$MeetModelCWProxy {
  /// This function **does support** nullification of nullable fields. All `null` values passed to `non-nullable` fields will be ignored.
  ///
  /// Usage
  /// ```dart
  /// MeetModel(...).copyWith(id: 12, name: "My name")
  /// ````
  MeetModel call({
    String name,
    String description,
    DateTime startTime,
    DateTime endTime,
    String activityCenterId,
    String coachId,
  });
}

/// Proxy class for `copyWith` functionality. This is a callable class and can be used as follows: `instanceOfMeetModel.copyWith(...)`.
class _$MeetModelCWProxyImpl implements _$MeetModelCWProxy {
  const _$MeetModelCWProxyImpl(this._value);

  final MeetModel _value;

  @override

  /// This function **does support** nullification of nullable fields. All `null` values passed to `non-nullable` fields will be ignored.
  ///
  /// Usage
  /// ```dart
  /// MeetModel(...).copyWith(id: 12, name: "My name")
  /// ````
  MeetModel call({
    Object? name = const $CopyWithPlaceholder(),
    Object? description = const $CopyWithPlaceholder(),
    Object? startTime = const $CopyWithPlaceholder(),
    Object? endTime = const $CopyWithPlaceholder(),
    Object? activityCenterId = const $CopyWithPlaceholder(),
    Object? coachId = const $CopyWithPlaceholder(),
  }) {
    return MeetModel(
      name: name == const $CopyWithPlaceholder()
          ? _value.name
          // ignore: cast_nullable_to_non_nullable
          : name as String,
      description: description == const $CopyWithPlaceholder()
          ? _value.description
          // ignore: cast_nullable_to_non_nullable
          : description as String,
      startTime: startTime == const $CopyWithPlaceholder()
          ? _value.startTime
          // ignore: cast_nullable_to_non_nullable
          : startTime as DateTime,
      endTime: endTime == const $CopyWithPlaceholder()
          ? _value.endTime
          // ignore: cast_nullable_to_non_nullable
          : endTime as DateTime,
      activityCenterId: activityCenterId == const $CopyWithPlaceholder()
          ? _value.activityCenterId
          // ignore: cast_nullable_to_non_nullable
          : activityCenterId as String,
      coachId: coachId == const $CopyWithPlaceholder()
          ? _value.coachId
          // ignore: cast_nullable_to_non_nullable
          : coachId as String,
    );
  }
}

extension $MeetModelCopyWith on MeetModel {
  /// Returns a callable class that can be used as follows: `instanceOfMeetModel.copyWith(...)`.
  // ignore: library_private_types_in_public_api
  _$MeetModelCWProxy get copyWith => _$MeetModelCWProxyImpl(this);
}

// **************************************************************************
// EmptyGenerator
// **************************************************************************

class MeetModelEmpty extends MeetModel {
  MeetModelEmpty()
      : super(
            name: '',
            description: '',
            startTime: DateTime.now(),
            endTime: DateTime.now(),
            activityCenterId: '',
            coachId: '');
}
