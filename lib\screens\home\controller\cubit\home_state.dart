import 'package:city_academy/core/shared/base_state.dart';
import 'package:city_academy/screens/home/<USER>/activity_center_model.dart';
import 'package:copy_with_extension/copy_with_extension.dart';
import 'package:empty_annotation/empty_annotation.dart';

part 'home_state.g.dart';

@CopyWith()
@Empty()
class HomeState extends BaseState {
  List<ActivityCenterModel> activityCenterList;
  ActivityCenterModel? selectedActivityCenter;

  HomeState({
    required this.activityCenterList,
    this.selectedActivityCenter,
    required super.loading,
    required super.error,
    required super.message,
  });
}
