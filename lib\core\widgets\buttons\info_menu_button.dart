import 'package:city_academy/core/widgets/buttons/info_row_button.dart';
import 'package:city_academy/screens/form/models/form_data.dart';
import 'package:city_academy/theme/colors.dart';
import 'package:flutter/material.dart';

class SectionButton extends StatelessWidget {
  final FormData formData;
  final String sectionTitle;
  final String buttonTitle;
  final void Function(BuildContext context, String sectionTitle) onTap;

  const SectionButton({
    super.key,
    required this.formData,
    required this.sectionTitle,
    required this.buttonTitle,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    bool missingRequired = false;
    Color borderColor = CustomColors.greenBase;

    try {
      final section = formData.sections.firstWhere(
        (s) => s.title == sectionTitle,
      );

      missingRequired = section.requiredFields?.any((requiredFieldName) {
            try {
              final field =
                  section.fields.firstWhere((f) => f.name == requiredFieldName);
              return field.value == null || field.value.toString().isEmpty;
            } catch (_) {
              return false;
            }
          }) ??
          false;

      borderColor =
          missingRequired ? CustomColors.errorRed : CustomColors.greenBase;
    } catch (_) {
      borderColor = CustomColors.neutralDark700;
    }

    return Container(
      decoration: BoxDecoration(
        border: Border.all(
          color: borderColor,
          width: 1,
        ),
        borderRadius: BorderRadius.circular(12),
      ),
      margin: const EdgeInsets.only(bottom: 32.0),
      child: InfoRowButton(
        onTap: () => onTap(context, sectionTitle),
        title: buttonTitle,
      ),
    );
  }
}
