class PersonalDataModel {
  final String email;
  final String birthDate;
  final String emergencyContact;
  final String phone;
  final String race;
  final String sexualOrientation;
  final String genderIdentity;
  final String address;

  const PersonalDataModel({
    required this.email,
    required this.birthDate,
    required this.emergencyContact,
    required this.phone,
    required this.race,
    required this.sexualOrientation,
    required this.genderIdentity,
    required this.address,
  });
}
