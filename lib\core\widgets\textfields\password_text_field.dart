import 'package:city_academy/core/util/validators.dart';
import 'package:city_academy/core/widgets/textfields/text_field_custom.dart';
import 'package:flutter/material.dart';

class PasswordTextfield extends TextFieldCustom {
  const PasswordTextfield({
    super.key,
    super.onChanged,
    super.onSubmit,
    super.errorMessage,
    super.isEnabled,
    super.controller,
    super.initialValue,
    super.textInputAction,
  }) : super(
          hintText: 'Senha',
          keyboardType: TextInputType.text,
          isPassword: true,
          validator: validateDefault,
        );
}
