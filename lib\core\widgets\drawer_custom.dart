import 'package:city_academy/core/util/extentions.dart';
import 'package:city_academy/core/util/global_instances.dart';
import 'package:city_academy/routes/app_routes.dart';
import 'package:city_academy/routes/routes.dart';
import 'package:city_academy/screens/login/controller/cubit/login_state.dart';
import 'package:city_academy/theme/colors.dart';
import 'package:city_academy/theme/texts.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class DrawerCustom extends StatelessWidget {
  const DrawerCustom({super.key});

  @override
  Widget build(BuildContext context) {
    return Drawer(
      width: MediaQuery.of(context).size.width / 2,
      child: Container(
        color: CustomColors.neutralWhite,
        child: BlocBuilder(
          bloc: loginCubit,
          builder: (context, LoginState state) {
            return Column(
              children: [
                Container(
                  margin: const EdgeInsets.all(24),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Container(
                        padding: const EdgeInsets.all(2),
                        decoration: BoxDecoration(
                          border: Border.all(
                            width: 1,
                            color: CustomColors.greenLight,
                          ),
                          shape: BoxShape.circle,
                        ),
                        child: ClipRRect(
                          borderRadius:
                              const BorderRadius.all(Radius.circular(90)),
                          child: SizedBox(
                            height: 35,
                            width: 35,
                            child: Image.network(
                              state.user.profileImageId,
                              fit: BoxFit.cover,
                              errorBuilder: (context, error, stackTrace) {
                                return const Icon(
                                  Icons.person,
                                  color: CustomColors.neutralGray500,
                                );
                              },
                            ),
                          ),
                        ),
                      ),
                      const SizedBox(width: 10),
                      Expanded(
                        flex: MediaQuery.of(context).size.width > 500 ? 0 : 2,
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              state.user.name.nameImprovident(),
                              softWrap: true,
                              style: titleText14.copyWith(
                                color: CustomColors.neutralDark700,
                                fontWeight: FontWeight.w600,
                              ),
                              overflow: TextOverflow.ellipsis,
                              maxLines: 2,
                            ),
                            if (state.user.document != null &&
                                state.user.document!.isNotEmpty)
                              Text(
                                'CPF: ${state.user.document}',
                                style: titleText12.copyWith(
                                  color: CustomColors.neutralGray500,
                                ),
                              ),
                            if (state.user.cns != null &&
                                state.user.cns!.isNotEmpty)
                              Text(
                                'CNS: ${state.user.cns}',
                                style: titleText12.copyWith(
                                  color: CustomColors.neutralGray500,
                                ),
                              ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
                const Divider(
                  thickness: 1,
                  indent: 30,
                  endIndent: 30,
                ),
                const SizedBox(height: 16),
                Expanded(
                  child: Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 24),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        TextButton.icon(
                          onPressed: () => router.push(Routes.studentSearch),
                          icon: const Icon(
                            Icons.person_search,
                            color: CustomColors.neutralGray500,
                          ),
                          label: Text(
                            'Buscar alunos',
                            style: titleText12.copyWith(
                              color: CustomColors.neutralGray500,
                            ),
                          ),
                        ),
                        const SizedBox(height: 12),
                        TextButton.icon(
                          onPressed: () => router.push(Routes.meet),
                          icon: const Icon(
                            Icons.post_add,
                            color: CustomColors.neutralGray500,
                          ),
                          label: Text(
                            'Criar reunião de equipe',
                            style: titleText12.copyWith(
                              color: CustomColors.neutralGray500,
                            ),
                          ),
                        ),
                        // Text(
                        //   "Versão ${PackageInformation.version}+${PackageInformation.buildNumber}",
                        //   style: titleText12.copyWith(
                        //     color: neutralMediumColor,
                        //
                        //   ),
                        // ),
                        const Expanded(child: SizedBox()),
                        SizedBox(
                          width: double.infinity,
                          child: TextButton.icon(
                            onPressed: () async {
                              await authService.logout();
                              loginCubit.reset();
                              if (context.mounted) {
                                router.go(Routes.login);
                              }
                            },
                            style: ButtonStyle(
                              backgroundColor: WidgetStateProperty.all(
                                CustomColors.accentSoftRed
                                    .withValues(alpha: 0.4),
                              ),
                              shape: WidgetStateProperty.all(
                                RoundedRectangleBorder(
                                  borderRadius: BorderRadius.circular(4),
                                ),
                              ),
                            ),
                            icon: const Icon(
                              Icons.exit_to_app_outlined,
                              color: CustomColors.errorRed,
                              size: 12,
                            ),
                            label: Text(
                              'Sair da conta',
                              style: titleText12.copyWith(
                                color: CustomColors.errorRed,
                              ),
                            ),
                          ),
                        ),
                        const SizedBox(height: 60),
                      ],
                    ),
                  ),
                ),
              ],
            );
          },
        ),
      ),
    );
  }
}
