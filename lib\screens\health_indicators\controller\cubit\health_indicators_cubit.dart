import 'package:city_academy/screens/health_indicators/controller/cubit/health_indicators_state.dart';
import 'package:city_academy/screens/health_indicators/controller/monitoring_service.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class HealthIndicatorsCubit extends Cubit<HealthIndicatorsState> {
  HealthIndicatorsCubit() : super(HealthIndicatorsStateEmpty());

  fetchHistoricalData() async {
    emit(state.copyWith(loading: true));

    try {
      final data = await MonitoringService().fetchHistoricalData();
      emit(state.copyWith(historicalData: data));
    } catch (e) {
      emit(state.copyWith(error: e.toString()));
    } finally {
      emit(state.copyWith(loading: false));
    }
  }

  void reset() => emit(HealthIndicatorsStateEmpty());
}
