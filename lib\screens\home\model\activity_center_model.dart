import 'package:copy_with_extension/copy_with_extension.dart';
import 'package:empty_annotation/empty_annotation.dart';

part 'activity_center_model.g.dart';

@CopyWith()
@Empty()
class ActivityCenterModel {
  final String id;
  final String activityCenterName;
  final String description;
  final String address;
  final int? district;
  final String? cnes;
  final String referencePoint;
  final double latitude;
  final double longitude;
  final DateTime createdAt;
  final DateTime updatedAt;

  ActivityCenterModel({
    required this.id,
    required this.activityCenterName,
    required this.description,
    required this.address,
    this.district,
    this.cnes,
    required this.referencePoint,
    required this.latitude,
    required this.longitude,
    required this.createdAt,
    required this.updatedAt,
  });

  factory ActivityCenterModel.fromJson(Map<String, dynamic> json) {
    return ActivityCenterModel(
      id: json['id'],
      activityCenterName: json['activity_center_name'],
      description: json['description'],
      address: json['address'],
      district: json['district'],
      cnes: json['cnes'],
      referencePoint: json['reference_point'],
      latitude: (json['latitude'] as num).toDouble(),
      longitude: (json['longitude'] as num).toDouble(),
      createdAt: DateTime.parse(json['created_at']),
      updatedAt: DateTime.parse(json['updated_at']),
    );
  }
}
