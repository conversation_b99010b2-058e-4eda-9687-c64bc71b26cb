import 'package:city_academy/core/util/app_texts.dart';
import 'package:city_academy/core/util/global_instances.dart';
import 'package:city_academy/screens/login/model/login_response_model.dart';
import 'package:city_academy/screens/login/model/user.dart';

class LoginRepository {
  Future<void> loginUser(String email, String password) async {
    final response = await unAuthClient.post(
      path: AppTexts.login,
      body: {'email': email, 'password': password},
    );

    final loginResponseModel = LoginResponseModel.fromJson(response.data);

    await storage.setAll({
      AppTexts.idTokenKey: loginResponseModel.idToken,
      AppTexts.refreshTokenKey: loginResponseModel.refreshToken,
      AppTexts.localId: loginResponseModel.localId,
    });

    // Após login, força verificação para garantir que está autenticado
    await authService.forceCheckAuthentication();
  }

  Future<User> getCoachInfo() async {
    final response = await authClient.get(path: AppTexts.getCoachInfo);

    return User.from<PERSON><PERSON>(response.data);
  }
}
