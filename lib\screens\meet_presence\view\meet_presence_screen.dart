import 'package:city_academy/core/util/constants.dart';
import 'package:city_academy/core/util/enums.dart';
import 'package:city_academy/core/util/global_instances.dart';
import 'package:city_academy/core/widgets/appbar_custom.dart';
import 'package:city_academy/core/widgets/buttons/custom_button.dart';
import 'package:city_academy/core/widgets/calendar_detail.dart';
import 'package:city_academy/core/widgets/custom_dialog.dart';
import 'package:city_academy/core/widgets/dropdowns/dropdown_custom.dart';
import 'package:city_academy/core/widgets/snack_bar.dart';
import 'package:city_academy/routes/app_routes.dart';
import 'package:city_academy/screens/meet_presence/controller/bloc/meet_presence_state.dart';
import 'package:city_academy/screens/meet_presence/view/widgets/list_coach_checkin.dart';
import 'package:city_academy/theme/colors.dart';
import 'package:city_academy/theme/texts.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class MeetPresenceScreen extends StatefulWidget {
  const MeetPresenceScreen({super.key});

  @override
  State<MeetPresenceScreen> createState() => _MeetPresenceScreenState();
}

class _MeetPresenceScreenState extends State<MeetPresenceScreen> {
  @override
  void initState() {
    super.initState();
    meetPresenceCubit.getCoaches();
  }

  @override
  Widget build(BuildContext context) {
    return BlocConsumer(
      bloc: meetPresenceCubit,
      listenWhen: (MeetPresenceState oldState, MeetPresenceState newState) =>
          oldState.status != newState.status,
      listener: (context, MeetPresenceState state) {
        if (state.status == EventCityAdminStatus.failed) {
          showDialog(
            context: context,
            builder: (BuildContext context) {
              return CustomDialog(
                title: 'Falha na confirmação',
                onPressed: router.pop,
                child: Text(
                  'Aconteceu algum problema ao fazer a confirmação de presença. Por favor tente novamente mais tarde.',
                  style: titleText14.copyWith(
                    color: CustomColors.neutralDark700,
                  ),
                ),
              );
            },
          );
        }
        if (state.status == EventCityAdminStatus.succeeds) {
          showDialog(
            context: context,
            builder: (BuildContext context) {
              return CustomDialog(
                title: 'Confirmação de presença realizada!',
                child: Text(
                  'Muito obrigado por realizar a confirmação de presença dos seus alunos, e boa aula',
                  style: titleText14.copyWith(
                    color: CustomColors.neutralDark700,
                    fontWeight: FontWeight.w400,
                  ),
                ),
                onPressed: () {
                  router.pop();
                  router.pop();
                },
              );
            },
          );
        }
      },
      builder: (context, MeetPresenceState state) {
        return Scaffold(
          backgroundColor: Colors.white,
          appBar: const AppbarCustom(title: 'Registrar presença'),
          body: SingleChildScrollView(
            child: Column(
              children: [
                CalendarDetails(eventCity: state.eventCity),
                const Divider(),
                const SizedBox(height: 26),
                Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 36),
                  child: DropdownCustom(
                    label: 'Tema',
                    value: state.meetingTopic,
                    onChanged: (value) {
                      meetPresenceCubit.onMeetingTopicChanged(value);
                    },
                    items: meetingTopics.entries
                        .map((entry) => DropdownMenuItem(
                              value: entry.key,
                              child: Text(entry.value),
                            ))
                        .toList(),
                  ),
                ),
                const SizedBox(height: 26),
                ListCoachCheckIn(
                  usersCheckIn: state.coaches,
                  listSelected: state.listSelected,
                ),
              ],
            ),
          ),
          bottomNavigationBar: Container(
            height: 90,
            decoration: const BoxDecoration(
              boxShadow: [
                BoxShadow(
                  color: Colors.grey,
                  offset: Offset(0, -2),
                  blurRadius: 2,
                ),
              ],
            ),
            child: Container(
              color: Colors.white,
              padding: const EdgeInsets.symmetric(horizontal: 30, vertical: 8),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceAround,
                children: [
                  Expanded(
                    flex: 2,
                    child: SizedBox(
                      height: double.infinity,
                      child: CustomButton(
                        title: 'Compareceu',
                        onPressed: meetPresenceCubit.isFormValid()
                            ? meetPresenceCubit.action
                            : () {
                                showErrorSnackbar(context,
                                    'Selecione um tema e pelo menos um participante');
                              },
                        loading: state.loading,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }
}
